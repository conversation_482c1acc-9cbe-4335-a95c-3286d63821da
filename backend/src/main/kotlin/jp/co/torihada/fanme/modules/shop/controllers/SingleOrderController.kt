package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.Const.RestrictedAccountType
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.RestrictedAccountController
import jp.co.torihada.fanme.modules.payment.controllers.CheckoutController
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.shop.Config
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.Util.OrderAmounts
import jp.co.torihada.fanme.modules.shop.Util.PurchasedItemStatus
import jp.co.torihada.fanme.modules.shop.controllers.OrderController.*
import jp.co.torihada.fanme.modules.shop.controllers.common.BaseOrderController
import jp.co.torihada.fanme.modules.shop.controllers.requests.SingleOrderRequest
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.OrderItem
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.usecases.order.UpdateOrder
import jp.co.torihada.fanme.modules.shop.usecases.singleOrder.*
import org.jboss.logging.Logger

@ApplicationScoped
class SingleOrderController : BaseOrderController() {
    @Inject private lateinit var logger: Logger
    @Inject private lateinit var shopConfig: Config
    @Inject private lateinit var checkSingleOrder: CheckoutSingleOrder
    @Inject private lateinit var createSingleOrder: CreateSingleOrder
    @Inject private lateinit var restrictedAccountController: RestrictedAccountController
    @Inject private lateinit var postSingleOrderProcessor: PostSingleOrderProcessor
    @Inject private lateinit var getAmounts: GetAmounts
    @Inject private lateinit var createSingleOrderItem: CreateSingleOrderItem
    @Inject private lateinit var updateOrder: UpdateOrder
    @Inject private lateinit var checkoutController: CheckoutController
    @Inject private lateinit var createPurchasedItems: CreatePurchasedItems

    fun createOrder(
        @Valid request: SingleOrderRequest.CreateOrder,
        fanmeToken: String?,
    ): OrderResult {
        // 注文内容のvalidation
        checkSingleOrder
            .execute(
                CheckoutSingleOrder.Input(
                    userUid = request.userId,
                    itemId = request.itemId,
                    quantity = request.quantity,
                )
            )
            .getOrElse { throw it }

        // amountsの計算
        val amounts =
            getAmounts
                .execute(
                    GetAmounts.Input(
                        itemId = request.itemId,
                        quantity = request.quantity,
                        tip = request.tip,
                    )
                )
                .getOrElse { throw it }

        val item = Item.findById(request.itemId)
        val shop = Shop.findById(item!!.shop.id!!)!!

        // checkoutの作成
        val checkout =
            createCheckout(
                shop = shop,
                amounts = amounts,
                fanmeToken = fanmeToken,
                userId = request.userId,
                tip = request.tip,
                paymentMethod = request.paymentMethod,
                convenienceParam = request.convenienceParam,
                isSingleOrder = true,
                itemId = request.itemId,
                quantity = request.quantity,
            )

        // orderの作成
        val order = createSingleOrderRecord(shop.id!!, checkout)

        // orderItemsの作成
        createSingleOrderItem(
            itemId = request.itemId,
            quantity = request.quantity,
            orderId = order.id!!,
        )

        // 決済の実行
        val resultExecuteOrder =
            executeOrder(
                sellerUserUid = shop.creatorUid!!,
                purchaserUserUid = request.userId,
                paymentMethod = request.paymentMethod,
                checkout = checkout,
                amounts = amounts,
                cardParam = request.cardParam,
                convenienceParam = request.convenienceParam,
                googlePayParam = request.googlePayParam,
                applePayParam = request.applePayParam,
                isSingleOrder = true,
            )

        // 注文後のレコード更新
        val resultPostOrderProcess =
            executePostOrderProcess(
                request = request,
                shop = shop,
                checkout = checkout,
                amounts = amounts,
                resultExecuteOrder = resultExecuteOrder,
            )

        // メール送信
        try {
            if (
                resultExecuteOrder.transaction != null &&
                    resultPostOrderProcess.purchasedItems?.isNotEmpty() == true
            ) {
                mailer.sendPurchasedEmail(
                    resultPostOrderProcess.order,
                    resultPostOrderProcess.purchasedItems,
                    resultExecuteOrder.transaction,
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to send email to purchaser", e)
        }
        try {
            if (
                resultExecuteOrder.transaction != null &&
                    resultPostOrderProcess.purchasedItems?.isNotEmpty() == true
            ) {
                val showSales =
                    !restrictedAccountController.isRestricted(
                        resultPostOrderProcess.order.shop.creatorUid,
                        RestrictedAccountType.HIDE_SALES,
                    )
                if (showSales) {
                    mailer.sendPurchasedSellerEmail(
                        resultPostOrderProcess.order,
                        resultPostOrderProcess.purchasedItems,
                        resultExecuteOrder.transaction,
                    )
                }
                mailer.sendPurchasedSellerEmailForManager(
                    resultPostOrderProcess.order,
                    resultPostOrderProcess.purchasedItems,
                    resultExecuteOrder.transaction,
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to send email to seller", e)
        }

        // エールログの追加
        if (resultExecuteOrder.transaction != null) {
            try {
                addYellLog(resultPostOrderProcess.order, checkout, resultExecuteOrder.status.value)
            } catch (e: Exception) {
                logger.error("Failed to insert yell log", e)
            }
        }

        return OrderResult(
            order = order,
            purchasedItems = resultPostOrderProcess.purchasedItems,
            convenienceCheckout = resultExecuteOrder.convenienceCheckout,
            redirectUrl = resultExecuteOrder.redirectUrl,
        )
    }

    fun finalizeCreditCard3DSecureSingleOrder(
        @Min(0) transactionId: Long,
        @NotNull @Min(0) checkoutId: Long,
    ) {
        val checkout = checkoutController.getCheckout(checkoutId)
        val order = Order.findByCheckoutId(checkoutId) ?: throw ResourceNotFoundException("Order")
        val transaction =
            Transaction.findById(transactionId) ?: throw ResourceNotFoundException("Transaction")

        val orderItem = OrderItem.findBySingleOrderId(order.id!!)

        val modifyRecordsFor3DSecureSingleOrderOutput =
            modifyRecordsFor3DSecureSingleOrder(
                transactionId = transactionId,
                checkout = checkout,
                order = order,
                orderItem = orderItem,
            )

        // メール送信
        try {
            mailer.sendPurchasedEmail(
                modifyRecordsFor3DSecureSingleOrderOutput.updatedOrder,
                modifyRecordsFor3DSecureSingleOrderOutput.purchasedItems,
                transaction,
            )
        } catch (e: Exception) {
            logger.error("Failed to send email to purchaser", e)
        }
        try {
            val showSales =
                !restrictedAccountController.isRestricted(
                    transaction.sellerUserId,
                    RestrictedAccountType.HIDE_SALES,
                )
            if (showSales) {
                mailer.sendPurchasedSellerEmail(
                    modifyRecordsFor3DSecureSingleOrderOutput.updatedOrder,
                    modifyRecordsFor3DSecureSingleOrderOutput.purchasedItems,
                    transaction,
                )
            }
            mailer.sendPurchasedSellerEmailForManager(
                modifyRecordsFor3DSecureSingleOrderOutput.updatedOrder,
                modifyRecordsFor3DSecureSingleOrderOutput.purchasedItems,
                transaction,
            )
        } catch (e: Exception) {
            logger.error("Failed to send email to seller", e)
        }

        addYellLog(
            order = modifyRecordsFor3DSecureSingleOrderOutput.updatedOrder,
            checkout = checkout,
            status = Const.CheckoutStatus.PAYSUCCESS.value,
        )
    }

    // finalizeCreditCard3DSecure関数内のみで使用
    // @Transactionalのためにスコープがpublicだが、外部から呼び出してはいけない
    @Transactional
    fun modifyRecordsFor3DSecureSingleOrder(
        transactionId: Long?,
        checkout: Checkout,
        order: Order,
        orderItem: OrderItem,
    ): ModifyRecordsFor3DSecureOutput {
        val updatedOrder =
            updateOrder
                .execute(
                    UpdateOrder.Input(
                        transactionId = transactionId,
                        checkout = checkout,
                        Const.CheckoutStatus.PAYSUCCESS.value,
                    )
                )
                .getOrElse { throw it }

        val amounts =
            getAmounts
                .execute(
                    GetAmounts.Input(
                        itemId = orderItem.item.id!!,
                        quantity = orderItem.quantity,
                        tip = checkout.tip?.amount ?: 0,
                    )
                )
                .getOrElse { throw it }

        // singleOrderの場合order_itemsテーブルからデータを取得してpurchaser_itemsテーブルに登録する
        val purchasedItems =
            createPurchasedItems
                .execute(
                    CreatePurchasedItems.Input(
                        purchaserUserId = checkout.purchaserUserId!!,
                        orderId = order.id!!,
                        amounts = amounts,
                        status = PurchasedItemStatus.PAYSUCCESS,
                        orderItemId = orderItem.id!!,
                    )
                )
                .getOrElse { throw it }
                .purchasedItems

        return ModifyRecordsFor3DSecureOutput(updatedOrder, listOf(purchasedItems))
    }

    @Transactional
    fun executePostOrderProcess(
        request: SingleOrderRequest.CreateOrder,
        shop: Shop,
        checkout: Checkout,
        amounts: OrderAmounts,
        resultExecuteOrder: ExecuteOrderResult,
    ): PostSingleOrderProcessor.Output {
        return postSingleOrderProcessor
            .execute(
                PostSingleOrderProcessor.Input(
                    userId = request.userId,
                    shop = shop,
                    transaction = resultExecuteOrder.transaction,
                    checkoutId = checkout.id!!,
                    amounts = amounts,
                    purchasedItemStatus =
                        PurchasedItemStatus.fromValue(resultExecuteOrder.status.value)
                            ?: Util.PurchasedItemStatus.PAYFAILED,
                    paymentMethod = request.paymentMethod,
                    itemId = request.itemId,
                    quantity = request.quantity,
                )
            )
            .getOrElse { throw it }
    }

    // createOrder関数内で使用
    // @Transactionalのためにスコープがpublicだが、外部から呼び出してはいけない
    @Transactional
    fun createSingleOrderRecord(shopId: Long, checkout: Checkout): Order {
        return createSingleOrder
            .execute(CreateSingleOrder.Input(shopId = shopId, checkoutId = checkout.id!!))
            .getOrElse { throw it }
    }

    @Transactional
    fun createSingleOrderItem(itemId: Long, quantity: Int, orderId: Long): OrderItem {
        return createSingleOrderItem
            .execute(
                CreateSingleOrderItem.Input(itemId = itemId, quantity = quantity, orderId = orderId)
            )
            .getOrElse { throw it }
    }
}
