package jp.co.torihada.fanme.modules.payment.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.PersistenceUnit
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.payment.Const

@PersistenceUnit(name = "payment")
@Entity
@Table(name = "seller_account_balances")
class SellerAccountBalance : BaseModel() {
    @Size(max = 10)
    @NotNull
    @Column(name = "tenant", nullable = false, length = 10)
    var tenant: String? = Const.DEFAULT_TENANT

    @Size(max = 50)
    @NotNull
    @Column(name = "seller_user_id", nullable = false, length = 255)
    var sellerUserId: String = ""

    @NotNull @Column(name = "amount", nullable = false) var amount: Int = 0

    @NotNull @Column(name = "accumulated_sales", nullable = false) var accumulatedSales: Int? = null

    companion object : PanacheCompanion<SellerAccountBalance> {
        fun findBySellerUserId(sellerUserId: String): SellerAccountBalance? {
            return find("sellerUserId", sellerUserId).firstResult()
        }

        fun findBySellerUserIds(sellerUserIds: List<String>): List<SellerAccountBalance> {
            if (sellerUserIds.isEmpty()) {
                return emptyList()
            }
            return find("sellerUserId in ?1", sellerUserIds).list()
        }

        fun findLast(): SellerAccountBalance? {
            return find("id = (select max(id) from SellerAccountBalance)").firstResult()
        }
    }
}
