package jp.co.torihada.fanme.modules.shop

object Const {
    const val DEFAULT_TENANT = "fanme"
    const val DEFAULT_MARGIN_RATE = 0.265f
    const val CHEKI_MARGIN_RATE = 0.3f
    const val DEFAULT_SHOP_MESSAGE = "購入ありがとうございます。\n大切に保存していただけると嬉しいです！"

    const val USER_UID_MAX_LENGTH = 50
    const val FILE_NAME_MAX_LENGTH = 30
    const val MIN_PRICE = 100L
    const val MAX_PRICE = 1000000L
    const val MIN_PROBABILITY = 0L
    const val MAX_PROBABILITY = 100L
    const val MIN_TIP = 100
    const val MAX_TIP = 300000
    const val MAX_CART_ITEM_QUANTITY = 99
    const val PURCHASER_COMMENT_MAX_LENGTH = 50

    const val CHEKI_DELIVERY_FEE = 400
    // チェキ一枚あたりにかかる原価
    const val CHEKI_COST = 90
    const val TIP_MARGIN_RATE = 0.15

    const val DATE_RESPONSE_FORMAT = "yyyy-MM-dd HH:mm:ss"
    const val DEFAULT_LOCAL_TIME_ZONE = "Asia/Tokyo"

    const val GACHA_SINGLE_PULL_COUNT = 1
    const val GACHA_MULTI_PULL_COUNT = 10

    object ValidationErrorMessages {
        const val CREATOR_UID_IS_REQUIRED = "creator_uid is required."
        const val CREATOR_UID_TOO_MANY_LENGTH = "creator_uid must be less than 50 characters."
        const val USER_ID_IS_REQUIRED = "user_id is required."
        const val USER_ID_TOO_MANY_LENGTH = "user_id must be less than 50 characters."
        const val SHOP_ID_IS_REQUIRED = "shop_id is required."
        const val SHOP_ID_MUST_BE_POSITIVE = "shop_id must be positive."
        const val ITEM_ID_IS_REQUIRED = "item_id is required."
        const val ITEM_ID_MUST_BE_POSITIVE = "item_id must be positive."
        const val ITEM_TYPE_IS_REQUIRED = "item_type is required."
        const val CART_ID_IS_REQUIRED = "cart_id is required."
        const val CART_ITEM_IDS_IS_REQUIRED = "cartItemIds is required."
        const val ORDER_ID_IS_REQUIRED = "orderId is required."

        const val QUANTITY_IS_REQUIRED = "quantity is required."
        const val QUANTITY_MUST_BE_POSITIVE = "quantity must be positive."
        const val QUANTITY_MUST_BE_LESS_THAN_OR_EQUAL_TO_MAX =
            "quantity must be less than or equal to $MAX_CART_ITEM_QUANTITY."
        const val PURCHASER_COMMENT_TOO_MANY_LENGTH =
            "purchase comment must be less than $PURCHASER_COMMENT_MAX_LENGTH characters."

        const val NAME_IS_REQUIRED = "name is required."
        const val NAME_TOO_MANY_LENGTH_30 = "name must be less than 30 characters."
        const val NAME_TOO_MANY_LENGTH_100 = "name must be less than 100 characters."

        const val DESCRIPTION_TOO_MANY_LENGTH_100 = "description must be less than 100 characters"
        const val DESCRIPTION_TOO_MANY_LENGTH_800 = "description must be less than 800 characters"
        const val DESCRIPTION_TOO_MANY_LENGTH_500 = "description must be less than 500 characters"

        const val HEADER_IMAGE_URL_IS_REQUIRED = "headerImageUrl is required."
        const val HEADER_IMAGE_URL_IS_INVALID_URL = "headerImageUrl must be a valid URL"

        const val MESSAGE_TOO_MANY_LENGTH = "message must be less than 100 characters"

        const val URI_IS_REQUIRED = "uri is required."
        const val URI_IS_INVALID_URL = "uri must be a valid URL"
        const val THUMBNAIL_URI_IS_REQUIRED = "thumbnailUri is required."
        const val THUMBNAIL_URI_IS_INVALID_URL = "thumbnailUri must be a valid URL"
        const val PRICE_IS_REQUIRED = "price is required."
        const val PRICE_MUST_BE_OR_MORE_100 = "price must be 100 or more."
        const val PRICE_MUST_BE_LESS_THAN_1000000 = "price must be less than 1000000."

        const val FILE_TYPE_IS_REQUIRED = "fileType is required."
        const val FILE_TYPE_MUST_BE_BINARY_NUMBER = "fileType must be binary number."
        const val FILE_TYPE_IS_INVALID_PATTERN = "fileType must be a valid pattern."
        const val AVAILABLE_IS_REQUIRED = "available is required."
        const val IS_DUPLICATED_IS_REQUIRED = "isDuplicated is required."

        const val FILES_IS_REQUIRED_AT_LEAST_ONE = "files must be at least 1."
        const val FILES_IS_INVALID_LENGTH = "files must be less than 5 and at least 1."

        const val SIZE_IS_REQUIRED = "size is required."
        const val SIZE_MUST_BE_POSITIVE = "size must be positive."

        const val CONDITION_TYPE_IS_REQUIRED = "conditionType is required."

        const val IS_SINGLE_SALE_IS_REQUIRED = "isSingleSale is required."

        const val QTY_TOTAL_MUST_BE_POSITIVE = "qtyTotal must be positive."
        const val QTY_TOTAL_MUST_BE_LESS_THAN_1000 = "qtyTotal must be less than 1000."

        const val PASSWORD_IS_TOO_MANY_LENGTH = "password must be less than 20 characters."

        const val DISCOUNT_RATE_MUST_BE_OR_MORE_1 = "discountRate must be 1 or more."
        const val DISCOUNT_RATE_MUST_BE_LESS_THAN_99 = "discountRate must be less than 99."

        const val TIP_MUST_BE_0_OR_MORE = "tip must be 0 or more."
        const val TIP_MUST_BE_LESS_THAN_100 = "tip must be 0 or more and less than 100."
        const val TIP_MUST_BE_LESS_THAN_300000 = "tip must be less than 300000."
        const val TIP_VALIDATION_MESSAGE = "Tip must be 0 or between $MIN_TIP and $MAX_TIP."

        const val PAYMENT_METHOD_IS_REQUIRED = "paymentMethod is required."

        const val CARD_SEQUENCE_IS_REQUIRED = "cardSequence is required."
        const val CARD_SEQUENCE_MUST_BE_POSITIVE = "cardSequence must be positive."

        const val CONVENIENCE_IS_REQUIRED = "convenience is required."
        const val CUSTOMER_NAME_IS_REQUIRED = "customerName is required."
        const val CUSTOMER_NAME_KANA_IS_REQUIRED = "customerKana is required."
        const val CUSTOMER_TELL_NO_IS_REQUIRED = "telNo is required."

        const val TOKEN_IS_REQUIRED = "token is required."
        const val ACCOUNT_IDENTITY_IS_REQUIRED = "accountIdentity is required."

        const val CART_ITEM_ID_IS_REQUIRED = "cartItemId is required."
        const val CART_ITEM_ID_MUST_BE_POSITIVE = "cartItemId must be positive."
        const val PRICE_MUST_BE_POSITIVE = "price must be positive."

        const val PROBABILITY_MUST_BE_POSITIVE = "Probability must be greater than or equal to 0"
        const val PROBABILITY_MUST_BE_LESS_THAN_OR_EQUAL_TO_100 =
            "Probability must be less than or equal to 100"
    }
}
