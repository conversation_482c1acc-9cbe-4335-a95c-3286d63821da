package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.validation.constraints.Min
import jp.co.torihada.fanme.Const.RestrictedAccountType
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.RestrictedAccountController
import jp.co.torihada.fanme.modules.payment.controllers.TransactionController
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.services.EmailService
import org.jboss.logging.Logger

@ApplicationScoped
class EmailController {
    @Inject private lateinit var logger: Logger
    @Inject private lateinit var transactionController: TransactionController
    @Inject private lateinit var restrictedAccountController: RestrictedAccountController
    @Inject private lateinit var mailer: EmailService

    fun sendPurchaseEmails(@Min(0) transactionId: Long): Result<Order, FanmeException> {
        val transaction = transactionController.getTransaction(transactionId)
        val order =
            Order.findByTransactionId(transaction.id!!)
                ?: return Err(ResourceNotFoundException("Order"))
        val purchasedItems = PurchasedItem.findByOrderId(order.id!!)
        if (purchasedItems.isEmpty()) return Err(ResourceNotFoundException("PurchasedItem"))
        try {
            mailer.sendPurchasedEmail(order, purchasedItems, transaction)
            val showSales =
                !restrictedAccountController.isRestricted(
                    order.shop.creatorUid,
                    RestrictedAccountType.HIDE_SALES,
                )
            if (showSales) {
                mailer.sendPurchasedSellerEmail(order, purchasedItems, transaction)
            }
            mailer.sendPurchasedSellerEmailForManager(order, purchasedItems, transaction)
        } catch (e: Exception) {
            logger.error("Failed to send email", e)
        }

        return Ok(order)
    }
}
