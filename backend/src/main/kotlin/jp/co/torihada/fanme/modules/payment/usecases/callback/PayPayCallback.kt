package jp.co.torihada.fanme.modules.payment.usecases.callback

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.ws.rs.core.NewCookie
import java.net.URI
import java.security.MessageDigest
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const.SUCCEED_PAYMENT_LOG_FORMAT
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import org.jboss.logging.Logger

@ApplicationScoped
class PayPayCallback {
    @Inject private lateinit var logger: Logger
    @Inject private lateinit var paymentConfig: PaymentConfig
    @Inject private lateinit var config: CommonConfig
    @Inject private lateinit var shopConfig: ShopConfig

    data class Input(
        val shopId: String,
        val orderId: String,
        val status: String,
        val tranDate: String,
        val payPayTrackingId: String?,
        val checkString: String,
        val errorInfo: String? = null,
        val errorCode: String? = null,
    )

    fun validateCheckString(request: Input, shopPass: String): Boolean {
        val inputString =
            request.shopId +
                request.orderId +
                request.status +
                request.tranDate +
                request.payPayTrackingId +
                shopPass
        val hashedValue =
            MessageDigest.getInstance("SHA-256").digest(inputString.toByteArray()).joinToString(
                ""
            ) {
                "%02x".format(it)
            }

        return hashedValue == request.checkString
    }

    data class Output(val url: String, val cookie: NewCookie?)

    fun execute(params: Input): Result<Output, FanmeException> {
        val shopId = params.shopId
        val orderId = params.orderId
        if (shopId != paymentConfig.gmoShopId()) {
            return Err(FanmeException(code = 0, message = "Invalid shop ID"))
        }

        validateCheckString(params, paymentConfig.gmoShopPass()).let {
            if (!it) {
                return Err(FanmeException(code = 0, message = "Invalid check string"))
            }
        }

        val fanmeToken = Checkout.getMetadata(orderId).fanmeToken
        val successUrl = Checkout.getMetadata(orderId).successUrl
        val errorUrl = Checkout.getMetadata(orderId).errorUrl

        var cookie: NewCookie? = null
        if (fanmeToken == null) {
            null
        } else {
            cookie =
                NewCookie.Builder(Const.PAYPAY_CALLBACK_TOKEN_KEY)
                    .value(fanmeToken)
                    .path("/")
                    .domain(URI(shopConfig.shopFrontUrl()).host)
                    .maxAge(NewCookie.DEFAULT_MAX_AGE)
                    .secure(false) // payment-serverとLBの通信はhttp(sなし)でされるため
                    .httpOnly(false)
                    .build()

            Checkout.updateMetadata(orderId) { it.copy(fanmeToken = null) }
        }

        if (params.errorCode != "" || params.errorInfo != "") {
            val checkout =
                Checkout.findByOrderId(orderId) ?: return Err(ResourceNotFoundException("Checkout"))
            logger.info(SUCCEED_PAYMENT_LOG_FORMAT.format("PAY_PAY", checkout.orderId, checkout.id))
            if (errorUrl == null) {
                return Err(FanmeException(code = 0, message = "Error URL is not set"))
            }
            return Ok(Output(errorUrl, cookie))
        } else {
            if (successUrl == null) {
                return Err(FanmeException(code = 0, message = "Success URL is not set"))
            }
            return Ok(Output(successUrl, cookie))
        }
    }
}
