package jp.co.torihada.fanme.modules.console.models

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.Util
import jp.co.torihada.fanme.modules.console.const.DateTime
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.fanme.models.User

enum class ConsoleUserRole(val value: String) {
    SUPER(UserRole.SUPER_VALUE),
    BIZ(UserRole.BIZ_VALUE),
    AGENT(UserRole.AGENT_VALUE),
    CREATOR(UserRole.CREATOR_VALUE);

    companion object {
        fun fromValue(value: String): ConsoleUserRole? {
            return entries.firstOrNull { it.value == value }
        }
    }
}

@Entity
@Table(name = "console_users")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
class ConsoleUser : BaseModel() {

    @NotNull @OneToOne @JoinColumn(name = "creator_id", nullable = false) var user: User = User()

    @Column(name = "agency_id") var agencyId: Long? = null

    @ManyToOne
    @JoinColumn(name = "agency_id", insertable = false, updatable = false)
    var agency: Agency? = null

    @NotNull @Column(name = "role", nullable = false) var role: String = UserRole.CREATOR_VALUE

    @Column(name = "deleted_at")
    @JsonFormat(
        pattern = DateTime.DATE_RESPONSE_FORMAT,
        timezone = DateTime.DEFAULT_LOCAL_TIME_ZONE,
    )
    var deletedAt: Instant? = null

    companion object : PanacheCompanion<ConsoleUser> {

        fun findByUserId(userId: Long): ConsoleUser? {
            return find("user.id = ?1 AND deletedAt IS NULL", userId).firstResult()
        }

        fun findByUserUuid(userUuid: String): ConsoleUser? {
            return find("user.uid = ?1 AND deletedAt IS NULL", userUuid).firstResult()
        }

        fun findByUserUid(uid: String): ConsoleUser? {
            return find("user.uid = ?1 AND deletedAt IS NULL", uid).firstResult()
        }

        fun findAll(top: Int?, skip: Int?): List<ConsoleUser> {
            val orderByQuery = " ORDER BY id ASC"
            val query = "deletedAt IS NULL"
            val find = find(query + orderByQuery)
            return Util.getEntityListWithPagination(find, top, skip).filterIsInstance<ConsoleUser>()
        }

        fun findByIds(ids: List<Long>): List<ConsoleUser> {
            if (ids.isEmpty()) {
                return emptyList()
            }
            return find("user.id IN ?1 AND deletedAt IS NULL", ids).list()
        }

        fun findByAgencyId(agencyId: Long): List<ConsoleUser> {
            return find("agencyId = ?1 AND deletedAt IS NULL", agencyId).list()
        }

        fun findByAgencyIdAndRole(agencyId: Long, role: String): List<ConsoleUser> {
            return find("agencyId = ?1 AND role = ?2 AND deletedAt IS NULL", agencyId, role).list()
        }
    }
}
