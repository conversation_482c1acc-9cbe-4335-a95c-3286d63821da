package jp.co.torihada.fanme.modules.payment.usecases.payments.common

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.Config as PaymentConfig
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Metadata
import jp.co.torihada.fanme.modules.payment.models.Tip
import kotlin.math.ceil

@ApplicationScoped
class CreateCheckout {

    @Inject private lateinit var config: Config

    @Inject private lateinit var paymentConfig: PaymentConfig

    data class Input(
        val sellerUserId: String,
        val purchaserUserId: String,
        val totalAmount: Int,
        val profit: Int,
        val fee: Int,
        val itemsAmount: Int,
        val tipAmount: Int,
        val paymentType: PaymentConst.PaymentType,
        val isDigital: Boolean,
        val deliveryFee: Int?,
        val convenience: PaymentConst.Conveniences?,
        val fanmeToken: String? = null,
        val successUrl: String? = null,
        val errorUrl: String? = null,
    )

    @Transactional
    fun execute(params: Input): Result<Checkout, FanmeException> {
        val checkout =
            Checkout().apply {
                this.tenant = config.tenant()
                this.type = params.paymentType.value
                this.sellerUserId = params.sellerUserId
                this.purchaserUserId = params.purchaserUserId
                this.providerShopId = getProviderShopId(params.isDigital)
                this.deliveryFee = params.deliveryFee
                this.amount = params.itemsAmount
                this.sellerSalesAmount = params.profit
                this.tenantSalesAmount = params.fee
                this.status = Const.CheckoutStatus.UNPROCESSED.value
                this.isShop = true
            }
        var totalAmount = params.totalAmount

        when (params.paymentType) {
            PaymentConst.PaymentType.CONVENIENCE -> {
                if (params.convenience == null) {
                    return Err(FanmeException(0, "cvsStoreType is required"))
                }
                val salesTaxRate = 0.1 // 消費税
                var cvsFee =
                    PaymentConst.CvsFees.findFeeByAmount(totalAmount, salesTaxRate)
                        ?: return Err(FanmeException(0, "failed to find cvs fee"))
                cvsFee = cvsFee.plus(ceil(cvsFee * salesTaxRate).toInt())
                totalAmount += cvsFee ?: 0
                checkout.apply {
                    this.cvsFee = cvsFee
                    this.convenience = params.convenience.value
                }
            }

            PaymentConst.PaymentType.PAY_PAY -> {
                checkout.apply {
                    this.metadata =
                        jacksonObjectMapper()
                            .writeValueAsString(
                                Metadata(
                                    fanmeToken = params.fanmeToken,
                                    successUrl = params.successUrl,
                                    errorUrl = params.errorUrl,
                                )
                            )
                }
            }
            PaymentConst.PaymentType.CREDIT_CARD -> {
                checkout.apply {
                    this.metadata =
                        jacksonObjectMapper()
                            .writeValueAsString(
                                Metadata(successUrl = params.successUrl, errorUrl = params.errorUrl)
                            )
                }
            }
            else -> {}
        }
        checkout.total = totalAmount
        checkout.persist()

        val orderId = createOrderId(checkout)
        checkout.orderId = orderId

        val tip =
            if (params.tipAmount > 0) {
                Tip().apply {
                    this.purchaserUserId = params.purchaserUserId
                    this.sellerUserId = params.sellerUserId
                    this.amount = params.tipAmount
                }
            } else null
        tip?.persist()
        checkout.tip = tip

        checkout.persist()
        return Ok(checkout)
    }

    private fun createOrderId(checkout: Checkout): String {
        val orderId =
            if (config.envKind() == "test") {
                "sampleOrderId"
            } else if (config.envKind() == "dev") {
                val localDateTime = LocalDateTime.now()
                val zonedDateTime = ZonedDateTime.of(localDateTime, ZoneId.systemDefault())
                config.envKind() + "-" + zonedDateTime.nano
            } else {
                config.envKind() + "-" + checkout.id
            }

        return orderId
    }

    private fun getProviderShopId(isDigital: Boolean): String {
        return paymentConfig.gmoShopId()

        // TODO 物販の審査が通ったらここで切り替える
        // if (isDigital) {
        //     return paymentConfig.gmoShopId()
        // } else {
        //     return paymentConfig.gmoProductShopId()
        // }
    }
}
