package jp.co.torihada.fanme.endpoints.shop

import com.fasterxml.jackson.annotation.JsonProperty
import io.vertx.ext.web.RoutingContext
import jakarta.inject.Inject
import jakarta.ws.rs.Consumes
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.Produces
import jakarta.ws.rs.core.Context
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.EmailController

@Path("/email")
class EmailEndpoint {
    @Inject private lateinit var handler: EmailController
    @Inject lateinit var util: Util

    data class SendEmailRequest(@JsonProperty("transaction_id") val transactionId: Long)

    @POST
    @Path("/payment")
    @Consumes("application/json")
    @Produces("application/json")
    fun sendPaymentEmail(requestBody: SendEmailRequest, @Context ctx: RoutingContext): Response {
        return try {
            if (!util.doBasicAuth(ctx)) {
                return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(UnAuthorizedException())
                    .build()
            }
            handler.sendPurchaseEmails(transactionId = requestBody.transactionId)
            Response.ok().build()
        } catch (e: Exception) {
            Response.serverError().build()
        }
    }
}
