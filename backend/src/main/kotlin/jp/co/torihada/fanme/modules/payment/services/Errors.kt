package jp.co.torihada.fanme.modules.payment.services

import com.fasterxml.jackson.annotation.JsonProperty

@Deprecated(
    "This class is migrated from fanme repository and should not be used for new development. Please use Exception.kt instead."
)
data class ErrorResponse(
    @JsonProperty("code") val code: Int,
    @JsonProperty("message") val message: String?,
)

// このクラスはfanmeリポジトリからの移行してきたものであり、必要なものだけを残す
// 新しいエラーを追加しない

@Deprecated(
    "This class is migrated from fanme repository and should not be used for new development. Please use Exception.kt instead."
)
object Errors {
    val CONNECTION_FAILED = ErrorResponse(404, "connection failed")

    // user
    val AUTHORIZATION_NOT_FOUND = ErrorResponse(401, "Authorization not found")
    val TOKEN_NOT_FOUND = ErrorResponse(401, "Token not found")
    val USER_NOT_FOUND = ErrorResponse(401, "User is not authenticated")
    val USER_NOT_MATCHED = ErrorResponse(401, "User is not matched")
    val APP_NOT_MATCHED = ErrorResponse(401, "App is not matched")
    val SELLER_APP_NOT_MATCHED = ErrorResponse(401, "Seller App is not matched")
    val APP_SELLER_USER_IS_DELETED = ErrorResponse(401, "App Seller User is deleted")

    // app
    val APP_INPUT_PASSWORD_INVALID = ErrorResponse(401, "App input password is invalid")

    // DB
    val TENANT_NOT_FOUND = ErrorResponse(404, "Tenant Not found")
    val APPS_NOT_FOUND = ErrorResponse(404, "Apps Not found")
    val SELLER_APPS_NOT_FOUND = ErrorResponse(404, "Seller Apps not found")
    val SELLER_APP_ITEMS_NOT_FOUND = ErrorResponse(404, "Seller App Items not found")
    val CREATOR_NOT_FOUND = ErrorResponse(404, "Creator not found")
    val SELLER_APP_ITEM_NOT_FOUND = ErrorResponse(404, "Seller App Item not found")
    val PURCHASER_PURCHASED_ITEM_NOT_FOUND =
        ErrorResponse(404, "Purchaser Purchased Item not found")
    val APP_NOT_PURCHASED = ErrorResponse(404, "App not purchased")
    val SELLER_APP_ITEM_OPTION_NOT_FOUND = ErrorResponse(404, "Seller App Item Option not found")
    val SELLER_APP_NOT_FOUND = ErrorResponse(404, "Seller App not found")
    val APP_PURCHASER_PURCHASED_ITEM_NOT_FOUND =
        ErrorResponse(404, "App Purchaser Purchased Item not found")
    val APP_PURCHASE_PASSWORD_NOT_SET = ErrorResponse(404, "App Purchase Password not set")
    val APP_SELLER_APP_ITEM_NOT_FOUND = ErrorResponse(404, "App Seller App Item not found")
    val APP_SELLER_APP_NOT_FOUND = ErrorResponse(404, "App Seller App not found")
    val APP_DELIVERY_COST_NOT_FOUND = ErrorResponse(404, "App delivery cost not found")
    val SELLER_ACCOUNT_BALANCE_NOT_FOUND = ErrorResponse(404, "Seller Account Balance not found")

    // S3
    val S3_UPLOAD_FAILED = ErrorResponse(500, "S3 upload FAILED")
    val S3_DELETE_FAILED = ErrorResponse(500, "S3 delete FAILED")
    val S3_DOWNLOAD_FAILED = ErrorResponse(500, "S3 download FAILED")

    // card
    val CARD_INVALID_PARAM = ErrorResponse(500, "Card invalid param")
    val CARD_SAVE_FAILED = ErrorResponse(500, "card save failed")
    val CARD_UPDATE_FAILED = ErrorResponse(500, "Card update failed")
    val CARD_DELETE_FAILED = ErrorResponse(500, "Card delete failed")
    val CARD_NOT_FOUND = ErrorResponse(500, "Card not found")

    // payment
    val PAYMENT_SAVED_CARD_FAILED = ErrorResponse(500, "Payment with saved card failed")
    val PAYMENT_ITEM_LIMIT_EXCEEDED = ErrorResponse(500, "Payment item limit exceeded")
    val PAYMENT_CARD_FAILED = ErrorResponse(500, "Payment card failed")
    val TIP_AMOUNT_EXCEEDED = ErrorResponse(500, "Tip amount exceeded")
    val GMO_SHOP_DATA_FAILED = ErrorResponse(500, "GMO shop data failed")
    val PAYMENT_APPLE_PAY_FAILED = ErrorResponse(500, "Apple pay failed")
    val PAYMENT_GOOGLE_PAY_FAILED = ErrorResponse(500, "Google pay failed")
    val PAYMENT_CVS_NOT_FOUND = ErrorResponse(500, "Payment cvs not found")
    val MINIAPP_WEBHOOK_FAILED = ErrorResponse(500, "Miniapp webhook failed")

    // apple
    val APPLE_TOKEN_FAILED = ErrorResponse(500, "Failed to retrieve the Apple token")

    // transfer
    val SELLER_BALANCE_NOT_ENOUGH = ErrorResponse(500, "Seller balance not enough")
    val SELLER_GMO_TRANSFER_ALREADY_REGISTERED =
        ErrorResponse(500, "Seller GMO transfer already registered")
    val GMO_DEPOSIT_REGISTRATION_FAILED = ErrorResponse(500, "GMO deposit registration failed")
}
