package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.dto.CreatorSales
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.utils.SecurityUtils
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.SellerAccountBalanceController
import jp.co.torihada.fanme.modules.payment.controllers.responses.SellerAccountBalanceResponse
import jp.co.torihada.fanme.modules.payment.dto.MonthlySalesDto

@ApplicationScoped
class GetAgencySales
@Inject
constructor(
    private val sellerAccountBalanceController: SellerAccountBalanceController,
    private val monthlySellerSalesController: MonthlySellerSalesController,
    private val securityUtils: SecurityUtils,
) {

    data class Input(
        val agencyId: Long,
        val fromYearMonth: String? = null,
        val toYearMonth: String? = null,
        val currentUserUid: String,
        val currentUserRole: ConsoleUserRole,
    )

    data class Output(val creatorSales: List<CreatorSales.CreatorSalesData>)

    fun execute(input: Input): Result<Output, FanmeException> {
        val agency = Agency.findById(input.agencyId)
        if (agency == null || agency.deletedAt != null) {
            return Err(ResourceNotFoundException("Agency not found"))
        }

        if (input.currentUserRole == ConsoleUserRole.AGENT) {
            val currentConsoleUser = ConsoleUser.findByUserUid(input.currentUserUid)
            try {
                securityUtils.validateAgentAccess(
                    input.currentUserRole.value,
                    currentConsoleUser?.agencyId,
                    input.agencyId,
                )
            } catch (e: FanmeException) {
                return Err(e)
            }
        }

        val userUids =
            ConsoleUser.findByAgencyIdAndRole(input.agencyId, UserRole.CREATOR_VALUE).mapNotNull {
                consoleUser ->
                consoleUser.user.uid
            }

        if (userUids.isEmpty()) {
            return Ok(Output(emptyList()))
        }

        val balancesResponse = sellerAccountBalanceController.getSellerAccountBalances(userUids)
        val balances = balancesResponse.balances

        val monthlySalesResponse =
            monthlySellerSalesController.getMonthlySellerSales(
                userUids,
                input.fromYearMonth,
                input.toYearMonth,
            )

        val monthlySales = monthlySalesResponse.monthlySales

        val users = User.findNotDeletedByUuids(userUids)
        val userIdToNameMap =
            users.mapNotNull { user -> user.uid?.let { it to (user.name ?: "") } }.toMap()

        val creatorSalesDataList =
            createSalesDataList(userUids, balances, monthlySales, userIdToNameMap)

        return Ok(Output(creatorSalesDataList))
    }

    private fun createSalesDataList(
        userIds: List<String>,
        balances: List<SellerAccountBalanceResponse.Balance>,
        monthlySales: List<MonthlySalesDto>,
        userIdToNameMap: Map<String, String>,
    ): List<CreatorSales.CreatorSalesData> {
        val monthlySalesBySellerMap =
            monthlySales
                .groupBy { sale -> sale.sellerUserId }
                .mapValues { entry -> entry.value.sortedByDescending { it.yearMonth } }

        val balanceMap = balances.associateBy { balance -> balance.sellerUserId }

        return userIds.map { sellerUserId ->
            val sellerBalance = balanceMap[sellerUserId]
            val monthlySalesList = monthlySalesBySellerMap[sellerUserId] ?: emptyList()

            CreatorSales.CreatorSalesData(
                creatorUid = sellerUserId,
                creatorName = userIdToNameMap[sellerUserId] ?: "FANMEユーザー",
                accumulatedSales = sellerBalance?.accumulatedSales ?: 0,
                withdrawableAmount = sellerBalance?.withdrawableAmount ?: 0,
                monthlySales =
                    monthlySalesList.map { sale ->
                        CreatorSales.MonthlySales(
                            yearMonth = sale.yearMonth,
                            sellerSalesAmount = sale.sellerSalesAmount,
                            merged = sale.merged,
                            expirationDate = sale.expirationDate,
                        )
                    },
            )
        }
    }
}
