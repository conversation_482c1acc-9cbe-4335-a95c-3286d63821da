package jp.co.torihada.fanme.endpoints.payment

import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import java.net.URI
import jp.co.torihada.fanme.modules.payment.controllers.CallbackController

@Path("/callback")
class CallbackEndpoints {
    @Inject lateinit var handler: CallbackController

    @POST
    @Path("/paypay")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    fun paypayCallback(
        @FormParam("ShopID") shopId: String,
        @FormParam("OrderID") orderId: String,
        @FormParam("Status") status: String,
        @FormParam("TranDate") tranDate: String,
        @FormParam("PayPayTrackingID") payPayTrackingId: String?,
        @FormParam("CheckString") checkString: String,
        @FormParam("ErrInfo") errorInfo: String?,
        @FormParam("ErrCode") errorCode: String?,
    ): Response {
        val response =
            handler.payPayCallback(
                shopId = shopId,
                orderId = orderId,
                status = status,
                tranDate = tranDate,
                payPayTrackingId = payPayTrackingId,
                checkString = checkString,
                errorInfo = errorInfo,
                errorCode = errorCode,
            )
        return if (response.cookie == null) {
            Response.seeOther(URI.create(response.url)).build()
        } else {
            Response.seeOther(URI.create(response.url)).cookie(response.cookie).build()
        }
    }

    @POST
    @Path("/credit-card-3d-secure")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    fun creditCard3DSecureCallback(
        @FormParam("requestorTransId") requestorTransId: String,
        @FormParam("event") event: String,
        @FormParam("param") param: String,
        @QueryParam("MD") md: String,
    ): Response {
        val response = handler.creditCard3DSecureCallback(md, event, param)
        return if (response.challengeUrl != null) {
            Response.seeOther(URI.create(response.challengeUrl)).build()
        } else {
            Response.seeOther(URI.create(response.returnUrl)).build()
        }
    }

    @POST
    @Path("/single-order/credit-card-3d-secure")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    fun creditCard3DSecureSingleOrderCallback(
        @FormParam("requestorTransId") requestorTransId: String,
        @FormParam("event") event: String,
        @FormParam("param") param: String,
        @QueryParam("MD") md: String,
    ): Response {
        val response = handler.creditCard3DSecureCallbackSingleOrder(md, event, param)
        return if (response.challengeUrl != null) {
            Response.seeOther(URI.create(response.challengeUrl)).build()
        } else {
            Response.seeOther(URI.create(response.returnUrl)).build()
        }
    }
}
