package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.InvalidChekiAmountException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.Const.TIP_MARGIN_RATE
import jp.co.torihada.fanme.modules.shop.Util.OrderAmounts
import jp.co.torihada.fanme.modules.shop.Util.UnitPrice
import jp.co.torihada.fanme.modules.shop.models.Cart
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.services.DeliveryService

@ApplicationScoped
class GetAmounts {
    @Inject private lateinit var deliveryService: DeliveryService

    data class Input(val cartId: Long, val cartItemIds: List<Long>, val tip: Int)

    fun execute(params: Input): Result<OrderAmounts, FanmeException> {
        val cart = Cart.findById(params.cartId) ?: return Err(ResourceNotFoundException("Cart"))
        val cartItems = cart.items.filter { params.cartItemIds.contains(it.id!!) }

        val isDigital = cartItems.all { it.item.isDigital }

        val unitPrices =
            cartItems
                .map { cartItem ->
                    var price = cartItem.singleFile?.price ?: cartItem.item.price
                    val sale = cartItem.item.onSale
                    if (sale != null) {
                        val now = Instant.now()
                        if (
                            !((sale.startAt != null && sale.startAt!!.isAfter(now)) ||
                                (sale.endAt != null && sale.endAt!!.isBefore(now)))
                        ) {
                            val discountRate = BigDecimal(sale.discountRate.toString())
                            price =
                                BigDecimal(price)
                                    .multiply(BigDecimal.ONE.subtract(discountRate))
                                    .setScale(0, RoundingMode.FLOOR)
                                    .toInt()
                        }
                    }
                    UnitPrice(cartItem.item, price, cartItem.quantity)
                }
                .toList()

        val deliveryFee = deliveryService.getDeliveryFee(cartItems.map { it.item })
        val itemAmount = unitPrices.sumOf { it.price * it.quantity }

        val fee =
            unitPrices.sumOf {
                val marginRate = BigDecimal(it.item.marginRate.toString())

                if (it.item.itemType == ItemType.CHEKI) {
                    calculateChekiMargin(it.price, it.quantity, marginRate).getOrElse { it ->
                        throw it
                    }
                } else {
                    BigDecimal(it.price * it.quantity)
                        .multiply(marginRate)
                        .setScale(0, RoundingMode.CEILING)
                        .toInt()
                }
            } +
                BigDecimal(params.tip)
                    .multiply(BigDecimal(TIP_MARGIN_RATE))
                    .setScale(0, RoundingMode.CEILING)
                    .toInt() +
                // 配送料はテナントの手数料に含める
                (deliveryFee ?: 0)

        val total = itemAmount + params.tip + (deliveryFee ?: 0)
        val profit = total - fee

        return Ok(
            OrderAmounts(
                total = total,
                itemAmount = itemAmount,
                profit = profit,
                fee = fee,
                tip = params.tip,
                unitPrices = unitPrices,
                deliveryFee = deliveryFee,
                isDigital = isDigital,
            )
        )
    }

    // 例: price = 1500円, quantity = 1, marginRate = 0.3 の場合
    // (1500 - 90) * 0.3 + 90 = 513
    private fun calculateChekiMargin(
        price: Int,
        quantity: Int,
        marginRate: BigDecimal,
    ): Result<Int, FanmeException> {
        if (price <= Const.CHEKI_COST) {
            return Err(InvalidChekiAmountException())
        }

        val chekiTotalCost = BigDecimal(Const.CHEKI_COST * quantity)
        val margin =
            BigDecimal(price * quantity)
                .minus(chekiTotalCost)
                .multiply(marginRate)
                .setScale(0, RoundingMode.CEILING)
                .plus(chekiTotalCost)
                .toInt()

        return Ok(margin)
    }
}
