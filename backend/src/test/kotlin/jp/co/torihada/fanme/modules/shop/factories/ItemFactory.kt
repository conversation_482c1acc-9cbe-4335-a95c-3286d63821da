package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.Shop

object ItemFactory {
    fun new(
        shopId: Long,
        name: String,
        description: String,
        itemType: ItemType = ItemType.DIGITAL_BUNDLE,
        thumbnailUri: String,
        thumbnailFrom: Int = 0,
        thumbnailBlurLevel: Int = 0,
        thumbnailWatermarkLevel: Int = 1,
        price: Int,
        fileType: Int = 0,
        available: Boolean = true,
        marginRate: Float = Const.DEFAULT_MARGIN_RATE,
        sortOrder: Int = 0,
    ): Item {
        return Item().apply {
            this.shop = Shop.findById(shopId)!!
            this.name = name
            this.description = description
            this.itemType = itemType
            this.thumbnailUri = thumbnailUri
            this.thumbnailFrom = thumbnailFrom
            this.thumbnailBlurLevel = thumbnailBlurLevel
            this.thumbnailWatermarkLevel = thumbnailWatermarkLevel
            this.price = price
            this.fileType = fileType
            this.available = available
            this.marginRate = marginRate
            this.sortOrder = sortOrder
        }
    }
}
