package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.notNullValue
import org.junit.jupiter.api.Test

@QuarkusTest
class AgencyUsersEndpointTest : AgencyEndpointTestBase() {

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `SUPER role can access any agency users`() {
        val agency = Agency.findAll().firstResult()
        val agencyId = agency?.id?.toString() ?: throw IllegalStateException("No agency found")

        given()
            .`when`()
            .get("/console/agencies/$agencyId/users")
            .then()
            .statusCode(200)
            .body("data.users.size()", equalTo(2))
            .body("data.users[0].name", notNullValue())
            .body("data.users[0].uid", notNullValue())
    }

    @Test
    @TestSecurity(
        user = "biz-user-1",
        roles = [UserRole.BIZ_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "biz-user-1")],
    )
    fun `BIZ role can access any agency users`() {
        val agency = Agency.findAll().firstResult()
        val agencyId = agency?.id?.toString() ?: throw IllegalStateException("No agency found")

        given()
            .`when`()
            .get("/console/agencies/$agencyId/users")
            .then()
            .statusCode(200)
            .body("data.users.size()", equalTo(2))
    }

    @Test
    @TestSecurity(
        user = "agent-user-1",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-user-1")],
    )
    fun `AGENT role can access their own agency users`() {
        val agency = Agency.findAll().firstResult()
        val agencyId = agency?.id?.toString() ?: throw IllegalStateException("No agency found")

        given()
            .`when`()
            .get("/console/agencies/$agencyId/users")
            .then()
            .statusCode(200)
            .body("data.users.size()", equalTo(2))
    }

    @Test
    @TestSecurity(
        user = "agent-user-1",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "agent-user-1")],
    )
    fun `AGENT role cannot access other agency users`() {
        val agencies = Agency.findAll().list()
        val unauthorizedAgency = agencies.find { it.name == "Unauthorized Agency" }
        val agencyId =
            unauthorizedAgency?.id?.toString()
                ?: throw IllegalStateException("Unauthorized agency not found")

        given().`when`().get("/console/agencies/$agencyId/users").then().statusCode(403)
    }

    @Test
    @TestSecurity(
        user = "test-agent",
        roles = [UserRole.AGENT_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "test-agent")],
    )
    fun `AGENT role can access their own agency with no creators`() {
        val agencies = Agency.findAll().list()
        val testAgency = agencies.find { it.name == "Test Agent Agency" }
        val agencyId =
            testAgency?.id?.toString() ?: throw IllegalStateException("Test agent agency not found")

        given()
            .`when`()
            .get("/console/agencies/$agencyId/users")
            .then()
            .statusCode(200)
            .body("data.users.size()", equalTo(0))
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `returns 404 for non-existent agency`() {
        given().`when`().get("/console/agencies/999/users").then().statusCode(404)
    }

    @Test
    fun `unauthenticated request returns 401`() {
        given().`when`().get("/console/agencies/1/users").then().statusCode(401)
    }
}
