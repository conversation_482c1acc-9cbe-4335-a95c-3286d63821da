package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemFile

object ItemFileFactory {
    fun new(
        itemId: Long,
        name: String? = null,
        objectUri: String? = null,
        thumbnailUri: String? = null,
        price: Int? = null,
        fileType: String = "image",
        size: Float = 1.0f,
        duration: Int = 0,
        itemThumbnailSelected: Boolean = false,
        sortOrder: Int = 0,
    ): ItemFile {
        return ItemFile().apply {
            this.item = Item.findById(itemId)!!
            this.name = name ?: "test name"
            this.objectUri = objectUri ?: "test objectUri"
            this.thumbnailUri = thumbnailUri ?: "test thumbnailUri"
            this.price = price
            this.fileType = fileType
            this.size = size
            this.duration = duration
            this.itemThumbnailSelected = itemThumbnailSelected
            this.sortOrder = sortOrder
        }
    }
}
