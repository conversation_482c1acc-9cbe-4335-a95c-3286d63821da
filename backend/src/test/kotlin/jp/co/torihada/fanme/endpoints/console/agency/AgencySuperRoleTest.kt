package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.ws.rs.core.MediaType
import java.time.Instant
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.payment.controllers.responses.MonthlySellerSalesResponse
import jp.co.torihada.fanme.modules.payment.dto.MonthlySalesDto
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.Test
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`

@QuarkusTest
class AgencySuperRoleTest : AgencyEndpointTestBase() {

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `SUPERロールでエージェンシー一覧を取得できること（全てのエージェンシー）`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencies.size()", equalTo(3))
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `SUPERロールでエージェンシーの売上データを取得できること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies/$agencyId/sales")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencySales.size()", equalTo(2))
            .body(
                "data.agencySales.find { it.creatorUid == 'creator-user-1' }.withdrawableAmount",
                equalTo(5000),
            )
            .body(
                "data.agencySales.find { it.creatorUid == 'creator-user-2' }.withdrawableAmount",
                equalTo(10000),
            )
            .body(
                "data.agencySales.find { it.creatorUid == 'creator-user-1' }.monthlySales.size()",
                equalTo(2),
            )
            .body(
                "data.agencySales.find { it.creatorUid == 'creator-user-2' }.monthlySales.size()",
                equalTo(2),
            )

        verify(sellerAccountBalanceController, times(1))
            .getSellerAccountBalances(listOf("creator-user-1", "creator-user-2"))

        verify(monthlySellerSalesController, times(1))
            .getMonthlySellerSales(listOf("creator-user-1", "creator-user-2"), null, null)
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `存在しないエージェンシーIDの場合は404が返ること`() {
        val nonExistentAgencyId = "999999"

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/agencies/$nonExistentAgencyId/sales")
            .then()
            .statusCode(404)
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `日付フィルターを指定して売上データを取得できること`() {
        val filteredMonthlySales1 =
            listOf(
                MonthlySalesDto(
                    sellerUserId = "creator-user-1",
                    yearMonth = "202401",
                    sellerSalesAmount = 3000,
                    merged = false,
                    expirationDate = Instant.now().plusSeconds(86400),
                )
            )

        val filteredMonthlySales2 =
            listOf(
                MonthlySalesDto(
                    sellerUserId = "creator-user-2",
                    yearMonth = "202401",
                    sellerSalesAmount = 5000,
                    merged = false,
                    expirationDate = Instant.now().plusSeconds(86400),
                )
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    listOf("creator-user-1", "creator-user-2"),
                    "202401",
                    "202401",
                )
            )
            .thenReturn(
                MonthlySellerSalesResponse.GetMonthlySales(
                    monthlySales = filteredMonthlySales1 + filteredMonthlySales2
                )
            )

        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .queryParam("from", "202401")
            .queryParam("to", "202401")
            .`when`()
            .get("/console/agencies/$agencyId/sales")
            .then()
            .statusCode(200)
            .body(notNullValue())
            .body("data.agencySales.size()", equalTo(2))
            .body(
                "data.agencySales.find { it.creatorUid == 'creator-user-1' }.monthlySales.size()",
                equalTo(1),
            )
            .body(
                "data.agencySales.find { it.creatorUid == 'creator-user-1' }.monthlySales[0].yearMonth",
                equalTo("202401"),
            )

        verify(sellerAccountBalanceController, times(1))
            .getSellerAccountBalances(listOf("creator-user-1", "creator-user-2"))

        verify(monthlySellerSalesController, times(1))
            .getMonthlySellerSales(listOf("creator-user-1", "creator-user-2"), "202401", "202401")
    }
}
