package jp.co.torihada.fanme

import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.importer.ClassFileImporter
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.lang.ArchRule
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SecurityArchitectureTests {
    private lateinit var importedClasses: JavaClasses

    @BeforeEach
    fun setup() {
        importedClasses =
            ClassFileImporter()
                .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_TESTS)
                .importPackages("jp.co.torihada.fanme")
    }

    private fun executeAndReport(rule: ArchRule, description: String) {
        println("\n=== セキュリティアーキテクチャテスト: $description ===")
        try {
            rule.check(importedClasses)
            println("✓ 違反はありません")
        } catch (e: AssertionError) {
            println("❌ 違反が検出されました:")
            println(e.message)
            throw e
        }
    }

    @Test
    fun `UseCase層でSecurityIdentityを使用しないこと`() {
        val rule =
            noClasses()
                .that()
                .resideInAnyPackage("..usecases..", "..usecase..")
                .should()
                .dependOnClassesThat()
                .haveFullyQualifiedName("io.quarkus.security.identity.SecurityIdentity")

        executeAndReport(rule, "UseCase層でSecurityIdentityを使用しないこと")
    }

    @Test
    fun `UseCase層でRequestContextを使用しないこと`() {
        val rule =
            noClasses()
                .that()
                .resideInAnyPackage("..usecases..", "..usecase..")
                .should()
                .dependOnClassesThat()
                .resideInAnyPackage("jakarta.ws.rs.container..", "javax.ws.rs.container..")
                .orShould()
                .dependOnClassesThat()
                .haveFullyQualifiedName("jakarta.ws.rs.core.Context")
                .orShould()
                .dependOnClassesThat()
                .haveFullyQualifiedName("javax.ws.rs.core.Context")

        executeAndReport(rule, "UseCase層でRequestContext関連クラスを使用しないこと")
    }

    @Test
    fun `UseCase層でSecurityIdentityを間接的にも使用しないこと`() {
        val rule =
            noClasses()
                .that()
                .resideInAnyPackage("..usecases..", "..usecase..")
                .should()
                .transitivelyDependOnClassesThat()
                .haveFullyQualifiedName("io.quarkus.security.identity.SecurityIdentity")

        executeAndReport(rule, "UseCase層でSecurityIdentityを間接的にも使用しないこと")
    }
}
