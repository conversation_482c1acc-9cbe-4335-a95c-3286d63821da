/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  DownloadUrlResponseBody,
  GetDownloadUrlRequest,
  GetPreSignedUrlRequest,
  GetUploadUrlRequest,
} from '../client-shop-api.schemas';
import type { Key } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Get Download Url
 */
export const getDownloadUrl = (getDownloadUrlRequest: GetDownloadUrlRequest) => {
  return customClientInstance<DownloadUrlResponseBody>({
    url: `/shops/current/files/download-url`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: getDownloadUrlRequest,
  });
};

export const getGetDownloadUrlMutationFetcher = () => {
  return (_: Key, { arg }: { arg: GetDownloadUrlRequest }): Promise<DownloadUrlResponseBody> => {
    return getDownloadUrl(arg);
  };
};
export const getGetDownloadUrlMutationKey = () => [`/shops/current/files/download-url`] as const;

export type GetDownloadUrlMutationResult = NonNullable<Awaited<ReturnType<typeof getDownloadUrl>>>;
export type GetDownloadUrlMutationError = void;

/**
 * @summary Get Download Url
 */
export const useGetDownloadUrl = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof getDownloadUrl>>,
    TError,
    Key,
    GetDownloadUrlRequest,
    Awaited<ReturnType<typeof getDownloadUrl>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getGetDownloadUrlMutationKey();
  const swrFn = getGetDownloadUrlMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Pre Signed Url
 */
export const getPreSignedUrl = (getPreSignedUrlRequest: GetPreSignedUrlRequest) => {
  return customClientInstance<void>({
    url: `/shops/current/files/presigned-url`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: getPreSignedUrlRequest,
  });
};

export const getGetPreSignedUrlMutationFetcher = () => {
  return (_: Key, { arg }: { arg: GetPreSignedUrlRequest }): Promise<void> => {
    return getPreSignedUrl(arg);
  };
};
export const getGetPreSignedUrlMutationKey = () => [`/shops/current/files/presigned-url`] as const;

export type GetPreSignedUrlMutationResult = NonNullable<Awaited<ReturnType<typeof getPreSignedUrl>>>;
export type GetPreSignedUrlMutationError = void;

/**
 * @summary Get Pre Signed Url
 */
export const useGetPreSignedUrl = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof getPreSignedUrl>>,
    TError,
    Key,
    GetPreSignedUrlRequest,
    Awaited<ReturnType<typeof getPreSignedUrl>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getGetPreSignedUrlMutationKey();
  const swrFn = getGetPreSignedUrlMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Upload Url
 */
export const getUploadUrl = (getUploadUrlRequest: GetUploadUrlRequest) => {
  return customClientInstance<void>({
    url: `/shops/current/files/upload-url`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: getUploadUrlRequest,
  });
};

export const getGetUploadUrlMutationFetcher = () => {
  return (_: Key, { arg }: { arg: GetUploadUrlRequest }): Promise<void> => {
    return getUploadUrl(arg);
  };
};
export const getGetUploadUrlMutationKey = () => [`/shops/current/files/upload-url`] as const;

export type GetUploadUrlMutationResult = NonNullable<Awaited<ReturnType<typeof getUploadUrl>>>;
export type GetUploadUrlMutationError = void;

/**
 * @summary Get Upload Url
 */
export const useGetUploadUrl = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof getUploadUrl>>,
    TError,
    Key,
    GetUploadUrlRequest,
    Awaited<ReturnType<typeof getUploadUrl>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getGetUploadUrlMutationKey();
  const swrFn = getGetUploadUrlMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
