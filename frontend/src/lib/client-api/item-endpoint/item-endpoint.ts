/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { GetItemsParams, ItemResponseBody } from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';

/**
 * @summary Get Items
 */
export const getItems = (creatorAccountIdentity: string, params?: GetItemsParams) => {
  return customClientInstance<void>({ url: `/shops/${creatorAccountIdentity}/items`, method: 'GET', params });
};

export const getGetItemsKey = (creatorAccountIdentity: string, params?: GetItemsParams) =>
  [`/shops/${creatorAccountIdentity}/items`, ...(params ? [params] : [])] as const;

export type GetItemsQueryResult = NonNullable<Awaited<ReturnType<typeof getItems>>>;
export type GetItemsQueryError = unknown;

/**
 * @summary Get Items
 */
export const useGetItems = <TError = unknown>(
  creatorAccountIdentity: string,
  params?: GetItemsParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getItems>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!creatorAccountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetItemsKey(creatorAccountIdentity, params) : null));
  const swrFn = () => getItems(creatorAccountIdentity, params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Item
 */
export const getItem = (creatorAccountIdentity: string, itemId: number) => {
  return customClientInstance<ItemResponseBody>({
    url: `/shops/${creatorAccountIdentity}/items/${itemId}`,
    method: 'GET',
  });
};

export const getGetItemKey = (creatorAccountIdentity: string, itemId: number) =>
  [`/shops/${creatorAccountIdentity}/items/${itemId}`] as const;

export type GetItemQueryResult = NonNullable<Awaited<ReturnType<typeof getItem>>>;
export type GetItemQueryError = void;

/**
 * @summary Get Item
 */
export const useGetItem = <TError = void>(
  creatorAccountIdentity: string,
  itemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getItem>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!(creatorAccountIdentity && itemId);
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetItemKey(creatorAccountIdentity, itemId) : null));
  const swrFn = () => getItem(creatorAccountIdentity, itemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
