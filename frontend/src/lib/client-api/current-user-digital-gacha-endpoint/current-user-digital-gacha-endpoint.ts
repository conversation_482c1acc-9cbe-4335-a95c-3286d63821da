/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  CreateDigitalGachaItemRequest,
  ItemResponseBody,
  UpdateDigitalGachaItemRequest,
} from '../client-shop-api.schemas';
import type { Key } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Create Digital Gacha Item
 */
export const createDigitalGachaItem = (createDigitalGachaItemRequest: CreateDigitalGachaItemRequest) => {
  return customClientInstance<ItemResponseBody>({
    url: `/shops/current/digital-gacha`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createDigitalGachaItemRequest,
  });
};

export const getCreateDigitalGachaItemMutationFetcher = () => {
  return (_: Key, { arg }: { arg: CreateDigitalGachaItemRequest }): Promise<ItemResponseBody> => {
    return createDigitalGachaItem(arg);
  };
};
export const getCreateDigitalGachaItemMutationKey = () => [`/shops/current/digital-gacha`] as const;

export type CreateDigitalGachaItemMutationResult = NonNullable<Awaited<ReturnType<typeof createDigitalGachaItem>>>;
export type CreateDigitalGachaItemMutationError = void;

/**
 * @summary Create Digital Gacha Item
 */
export const useCreateDigitalGachaItem = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof createDigitalGachaItem>>,
    TError,
    Key,
    CreateDigitalGachaItemRequest,
    Awaited<ReturnType<typeof createDigitalGachaItem>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateDigitalGachaItemMutationKey();
  const swrFn = getCreateDigitalGachaItemMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Digital Gacha Item
 */
export const updateDigitalGachaItem = (
  itemId: number,
  updateDigitalGachaItemRequest: UpdateDigitalGachaItemRequest,
) => {
  return customClientInstance<ItemResponseBody>({
    url: `/shops/current/digital-gacha/${itemId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateDigitalGachaItemRequest,
  });
};

export const getUpdateDigitalGachaItemMutationFetcher = (itemId: number) => {
  return (_: Key, { arg }: { arg: UpdateDigitalGachaItemRequest }): Promise<ItemResponseBody> => {
    return updateDigitalGachaItem(itemId, arg);
  };
};
export const getUpdateDigitalGachaItemMutationKey = (itemId: number) =>
  [`/shops/current/digital-gacha/${itemId}`] as const;

export type UpdateDigitalGachaItemMutationResult = NonNullable<Awaited<ReturnType<typeof updateDigitalGachaItem>>>;
export type UpdateDigitalGachaItemMutationError = void;

/**
 * @summary Update Digital Gacha Item
 */
export const useUpdateDigitalGachaItem = <TError = void>(
  itemId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateDigitalGachaItem>>,
      TError,
      Key,
      UpdateDigitalGachaItemRequest,
      Awaited<ReturnType<typeof updateDigitalGachaItem>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateDigitalGachaItemMutationKey(itemId);
  const swrFn = getUpdateDigitalGachaItemMutationFetcher(itemId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
