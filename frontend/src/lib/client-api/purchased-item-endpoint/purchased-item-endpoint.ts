/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { GetPurchasedItemsParams, PurchaseItemResponseBody } from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';

/**
 * @summary Get Purchased Items
 */
export const getPurchasedItems = (params?: GetPurchasedItemsParams) => {
  return customClientInstance<void>({ url: `/shops/current/purchased-items`, method: 'GET', params });
};

export const getGetPurchasedItemsKey = (params?: GetPurchasedItemsParams) =>
  [`/shops/current/purchased-items`, ...(params ? [params] : [])] as const;

export type GetPurchasedItemsQueryResult = NonNullable<Awaited<ReturnType<typeof getPurchasedItems>>>;
export type GetPurchasedItemsQueryError = void;

/**
 * @summary Get Purchased Items
 */
export const useGetPurchasedItems = <TError = void>(
  params?: GetPurchasedItemsParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getPurchasedItems>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetPurchasedItemsKey(params) : null));
  const swrFn = () => getPurchasedItems(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Purchased Item
 */
export const getPurchasedItem = (purchasedItemId: number) => {
  return customClientInstance<PurchaseItemResponseBody>({
    url: `/shops/current/purchased-items/${purchasedItemId}`,
    method: 'GET',
  });
};

export const getGetPurchasedItemKey = (purchasedItemId: number) =>
  [`/shops/current/purchased-items/${purchasedItemId}`] as const;

export type GetPurchasedItemQueryResult = NonNullable<Awaited<ReturnType<typeof getPurchasedItem>>>;
export type GetPurchasedItemQueryError = void;

/**
 * @summary Get Purchased Item
 */
export const useGetPurchasedItem = <TError = void>(
  purchasedItemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getPurchasedItem>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!purchasedItemId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetPurchasedItemKey(purchasedItemId) : null));
  const swrFn = () => getPurchasedItem(purchasedItemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
