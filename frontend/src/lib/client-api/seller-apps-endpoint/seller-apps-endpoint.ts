/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { Key, SWRConfiguration } from 'swr';

/**
 * @summary Get Digital Fan Letter Link
 */
export const getDigitalFanLetterLink = (creatorAccountIdentity: string) => {
  return customClientInstance<void>({
    url: `/shops/${creatorAccountIdentity}/seller-apps/digital-fan-letter-link`,
    method: 'GET',
  });
};

export const getGetDigitalFanLetterLinkKey = (creatorAccountIdentity: string) =>
  [`/shops/${creatorAccountIdentity}/seller-apps/digital-fan-letter-link`] as const;

export type GetDigitalFanLetterLinkQueryResult = NonNullable<Awaited<ReturnType<typeof getDigitalFanLetterLink>>>;
export type GetDigitalFanLetterLinkQueryError = unknown;

/**
 * @summary Get Digital Fan Letter Link
 */
export const useGetDigitalFanLetterLink = <TError = unknown>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getDigitalFanLetterLink>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!creatorAccountIdentity;
  const swrKey =
    swrOptions?.swrKey ?? (() => (isEnabled ? getGetDigitalFanLetterLinkKey(creatorAccountIdentity) : null));
  const swrFn = () => getDigitalFanLetterLink(creatorAccountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
