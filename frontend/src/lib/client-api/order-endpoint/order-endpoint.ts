/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  FinalizeCreditCard3DSecureRequest,
  TipLimitResponseBody,
  UpdateOrderRequest,
} from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Update Order
 */
export const updateOrder = (updateOrderRequest: UpdateOrderRequest) => {
  return customClientInstance<void>({
    url: `/orders`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateOrderRequest,
  });
};

export const getUpdateOrderMutationFetcher = () => {
  return (_: Key, { arg }: { arg: UpdateOrderRequest }): Promise<void> => {
    return updateOrder(arg);
  };
};
export const getUpdateOrderMutationKey = () => [`/orders`] as const;

export type UpdateOrderMutationResult = NonNullable<Awaited<ReturnType<typeof updateOrder>>>;
export type UpdateOrderMutationError = void;

/**
 * @summary Update Order
 */
export const useUpdateOrder = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof updateOrder>>,
    TError,
    Key,
    UpdateOrderRequest,
    Awaited<ReturnType<typeof updateOrder>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateOrderMutationKey();
  const swrFn = getUpdateOrderMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Orders
 */
export const getOrders = () => {
  return customClientInstance<void>({ url: `/orders`, method: 'GET' });
};

export const getGetOrdersKey = () => [`/orders`] as const;

export type GetOrdersQueryResult = NonNullable<Awaited<ReturnType<typeof getOrders>>>;
export type GetOrdersQueryError = void;

/**
 * @summary Get Orders
 */
export const useGetOrders = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getOrders>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetOrdersKey() : null));
  const swrFn = () => getOrders();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Convenience Fees
 */
export const getConvenienceFees = () => {
  return customClientInstance<void>({ url: `/orders/convenience-fees`, method: 'GET' });
};

export const getGetConvenienceFeesKey = () => [`/orders/convenience-fees`] as const;

export type GetConvenienceFeesQueryResult = NonNullable<Awaited<ReturnType<typeof getConvenienceFees>>>;
export type GetConvenienceFeesQueryError = unknown;

/**
 * @summary Get Convenience Fees
 */
export const useGetConvenienceFees = <TError = unknown>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getConvenienceFees>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetConvenienceFeesKey() : null));
  const swrFn = () => getConvenienceFees();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Finalize Credit Card 3 D Secure
 */
export const finalizeCreditCard3DSecure = (finalizeCreditCard3DSecureRequest: FinalizeCreditCard3DSecureRequest) => {
  return customClientInstance<void>({
    url: `/orders/finalize-credit-card-3d-secure`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: finalizeCreditCard3DSecureRequest,
  });
};

export const getFinalizeCreditCard3DSecureMutationFetcher = () => {
  return (_: Key, { arg }: { arg: FinalizeCreditCard3DSecureRequest }): Promise<void> => {
    return finalizeCreditCard3DSecure(arg);
  };
};
export const getFinalizeCreditCard3DSecureMutationKey = () => [`/orders/finalize-credit-card-3d-secure`] as const;

export type FinalizeCreditCard3DSecureMutationResult = NonNullable<
  Awaited<ReturnType<typeof finalizeCreditCard3DSecure>>
>;
export type FinalizeCreditCard3DSecureMutationError = void;

/**
 * @summary Finalize Credit Card 3 D Secure
 */
export const useFinalizeCreditCard3DSecure = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof finalizeCreditCard3DSecure>>,
    TError,
    Key,
    FinalizeCreditCard3DSecureRequest,
    Awaited<ReturnType<typeof finalizeCreditCard3DSecure>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getFinalizeCreditCard3DSecureMutationKey();
  const swrFn = getFinalizeCreditCard3DSecureMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Tip Limit
 */
export const getTipLimit = (creatorAccountIdentity: string) => {
  return customClientInstance<TipLimitResponseBody>({
    url: `/orders/tip-upper-limit/${creatorAccountIdentity}`,
    method: 'GET',
  });
};

export const getGetTipLimitKey = (creatorAccountIdentity: string) =>
  [`/orders/tip-upper-limit/${creatorAccountIdentity}`] as const;

export type GetTipLimitQueryResult = NonNullable<Awaited<ReturnType<typeof getTipLimit>>>;
export type GetTipLimitQueryError = void;

/**
 * @summary Get Tip Limit
 */
export const useGetTipLimit = <TError = void>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getTipLimit>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!creatorAccountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetTipLimitKey(creatorAccountIdentity) : null));
  const swrFn = () => getTipLimit(creatorAccountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
