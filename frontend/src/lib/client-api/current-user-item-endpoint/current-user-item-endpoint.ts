/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  CreateOrUpdateItemRequest,
  GetCurrentUserItemParams,
  GetCurrentUserItemsParams,
  ItemResponseBody,
  SortItemsRequest,
} from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Get Items
 */
export const getCurrentUserItems = (params?: GetCurrentUserItemsParams) => {
  return customClientInstance<void>({ url: `/shops/current/items`, method: 'GET', params });
};

export const getGetCurrentUserItemsKey = (params?: GetCurrentUserItemsParams) =>
  [`/shops/current/items`, ...(params ? [params] : [])] as const;

export type GetCurrentUserItemsQueryResult = NonNullable<Awaited<ReturnType<typeof getCurrentUserItems>>>;
export type GetCurrentUserItemsQueryError = void;

/**
 * @summary Get Items
 */
export const useGetCurrentUserItems = <TError = void>(
  params?: GetCurrentUserItemsParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getCurrentUserItems>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCurrentUserItemsKey(params) : null));
  const swrFn = () => getCurrentUserItems(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create Item
 */
export const createItem = (createOrUpdateItemRequest: CreateOrUpdateItemRequest) => {
  return customClientInstance<void>({
    url: `/shops/current/items`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createOrUpdateItemRequest,
  });
};

export const getCreateItemMutationFetcher = () => {
  return (_: Key, { arg }: { arg: CreateOrUpdateItemRequest }): Promise<void> => {
    return createItem(arg);
  };
};
export const getCreateItemMutationKey = () => [`/shops/current/items`] as const;

export type CreateItemMutationResult = NonNullable<Awaited<ReturnType<typeof createItem>>>;
export type CreateItemMutationError = void;

/**
 * @summary Create Item
 */
export const useCreateItem = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof createItem>>,
    TError,
    Key,
    CreateOrUpdateItemRequest,
    Awaited<ReturnType<typeof createItem>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateItemMutationKey();
  const swrFn = getCreateItemMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Sort Items
 */
export const sortItems = (sortItemsRequest: SortItemsRequest) => {
  return customClientInstance<void>({
    url: `/shops/current/items/sort`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: sortItemsRequest,
  });
};

export const getSortItemsMutationFetcher = () => {
  return (_: Key, { arg }: { arg: SortItemsRequest }): Promise<void> => {
    return sortItems(arg);
  };
};
export const getSortItemsMutationKey = () => [`/shops/current/items/sort`] as const;

export type SortItemsMutationResult = NonNullable<Awaited<ReturnType<typeof sortItems>>>;
export type SortItemsMutationError = void;

/**
 * @summary Sort Items
 */
export const useSortItems = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof sortItems>>,
    TError,
    Key,
    SortItemsRequest,
    Awaited<ReturnType<typeof sortItems>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getSortItemsMutationKey();
  const swrFn = getSortItemsMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Item
 */
export const updateItem = (itemId: number, createOrUpdateItemRequest: CreateOrUpdateItemRequest) => {
  return customClientInstance<void>({
    url: `/shops/current/items/${itemId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: createOrUpdateItemRequest,
  });
};

export const getUpdateItemMutationFetcher = (itemId: number) => {
  return (_: Key, { arg }: { arg: CreateOrUpdateItemRequest }): Promise<void> => {
    return updateItem(itemId, arg);
  };
};
export const getUpdateItemMutationKey = (itemId: number) => [`/shops/current/items/${itemId}`] as const;

export type UpdateItemMutationResult = NonNullable<Awaited<ReturnType<typeof updateItem>>>;
export type UpdateItemMutationError = void;

/**
 * @summary Update Item
 */
export const useUpdateItem = <TError = void>(
  itemId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateItem>>,
      TError,
      Key,
      CreateOrUpdateItemRequest,
      Awaited<ReturnType<typeof updateItem>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateItemMutationKey(itemId);
  const swrFn = getUpdateItemMutationFetcher(itemId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Item
 */
export const getCurrentUserItem = (itemId: number, params?: GetCurrentUserItemParams) => {
  return customClientInstance<ItemResponseBody>({ url: `/shops/current/items/${itemId}`, method: 'GET', params });
};

export const getGetCurrentUserItemKey = (itemId: number, params?: GetCurrentUserItemParams) =>
  [`/shops/current/items/${itemId}`, ...(params ? [params] : [])] as const;

export type GetCurrentUserItemQueryResult = NonNullable<Awaited<ReturnType<typeof getCurrentUserItem>>>;
export type GetCurrentUserItemQueryError = void;

/**
 * @summary Get Item
 */
export const useGetCurrentUserItem = <TError = void>(
  itemId: number,
  params?: GetCurrentUserItemParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getCurrentUserItem>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!itemId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCurrentUserItemKey(itemId, params) : null));
  const swrFn = () => getCurrentUserItem(itemId, params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
