/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { CreateShopRequest, UpdateShopRequest } from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Update Shop
 */
export const updateShop = (updateShopRequest: UpdateShopRequest) => {
  return customClientInstance<void>({
    url: `/shops/current`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateShopRequest,
  });
};

export const getUpdateShopMutationFetcher = () => {
  return (_: Key, { arg }: { arg: UpdateShopRequest }): Promise<void> => {
    return updateShop(arg);
  };
};
export const getUpdateShopMutationKey = () => [`/shops/current`] as const;

export type UpdateShopMutationResult = NonNullable<Awaited<ReturnType<typeof updateShop>>>;
export type UpdateShopMutationError = void;

/**
 * @summary Update Shop
 */
export const useUpdateShop = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof updateShop>>,
    TError,
    Key,
    UpdateShopRequest,
    Awaited<ReturnType<typeof updateShop>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateShopMutationKey();
  const swrFn = getUpdateShopMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Shop
 */
export const getCurrentUserShop = () => {
  return customClientInstance<void>({ url: `/shops/current`, method: 'GET' });
};

export const getGetCurrentUserShopKey = () => [`/shops/current`] as const;

export type GetCurrentUserShopQueryResult = NonNullable<Awaited<ReturnType<typeof getCurrentUserShop>>>;
export type GetCurrentUserShopQueryError = void;

/**
 * @summary Get Shop
 */
export const useGetCurrentUserShop = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getCurrentUserShop>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCurrentUserShopKey() : null));
  const swrFn = () => getCurrentUserShop();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create Shop
 */
export const createShop = (createShopRequest: CreateShopRequest) => {
  return customClientInstance<void>({
    url: `/shops/current`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createShopRequest,
  });
};

export const getCreateShopMutationFetcher = () => {
  return (_: Key, { arg }: { arg: CreateShopRequest }): Promise<void> => {
    return createShop(arg);
  };
};
export const getCreateShopMutationKey = () => [`/shops/current`] as const;

export type CreateShopMutationResult = NonNullable<Awaited<ReturnType<typeof createShop>>>;
export type CreateShopMutationError = void;

/**
 * @summary Create Shop
 */
export const useCreateShop = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof createShop>>,
    TError,
    Key,
    CreateShopRequest,
    Awaited<ReturnType<typeof createShop>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateShopMutationKey();
  const swrFn = getCreateShopMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
