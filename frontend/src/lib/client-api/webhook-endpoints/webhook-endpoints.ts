/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { GmoWebhookBody } from '../client-shop-api.schemas';
import type { Key } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Gmo Webhook
 */
export const gmoWebhook = (gmoWebhookBody: GmoWebhookBody) => {
  const formUrlEncoded = new URLSearchParams();
  formUrlEncoded.append(`ShopID`, gmoWebhookBody.ShopID);
  formUrlEncoded.append(`ShopPass`, gmoWebhookBody.ShopPass);
  formUrlEncoded.append(`AccessID`, gmoWebhookBody.AccessID);
  formUrlEncoded.append(`AccessPass`, gmoWebhookBody.AccessPass);
  formUrlEncoded.append(`OrderID`, gmoWebhookBody.OrderID);
  formUrlEncoded.append(`Status`, gmoWebhookBody.Status);
  formUrlEncoded.append(`Amount`, gmoWebhookBody.Amount);
  formUrlEncoded.append(`Tax`, gmoWebhookBody.Tax);
  formUrlEncoded.append(`PayType`, gmoWebhookBody.PayType);

  return customClientInstance<void>({
    url: `/webhook/gmo/payment/notice`,
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: formUrlEncoded,
  });
};

export const getGmoWebhookMutationFetcher = () => {
  return (_: Key, { arg }: { arg: GmoWebhookBody }): Promise<void> => {
    return gmoWebhook(arg);
  };
};
export const getGmoWebhookMutationKey = () => [`/webhook/gmo/payment/notice`] as const;

export type GmoWebhookMutationResult = NonNullable<Awaited<ReturnType<typeof gmoWebhook>>>;
export type GmoWebhookMutationError = void;

/**
 * @summary Gmo Webhook
 */
export const useGmoWebhook = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof gmoWebhook>>,
    TError,
    Key,
    GmoWebhookBody,
    Awaited<ReturnType<typeof gmoWebhook>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getGmoWebhookMutationKey();
  const swrFn = getGmoWebhookMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
