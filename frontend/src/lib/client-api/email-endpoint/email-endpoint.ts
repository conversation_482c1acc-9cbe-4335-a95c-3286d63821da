/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { SendEmailRequest } from '../client-shop-api.schemas';
import type { Key } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Send Payment Email
 */
export const sendPaymentEmail = (sendEmailRequest: SendEmailRequest) => {
  return customClientInstance<void>({
    url: `/email/payment`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: sendEmailRequest,
  });
};

export const getSendPaymentEmailMutationFetcher = () => {
  return (_: Key, { arg }: { arg: SendEmailRequest }): Promise<void> => {
    return sendPaymentEmail(arg);
  };
};
export const getSendPaymentEmailMutationKey = () => [`/email/payment`] as const;

export type SendPaymentEmailMutationResult = NonNullable<Awaited<ReturnType<typeof sendPaymentEmail>>>;
export type SendPaymentEmailMutationError = void;

/**
 * @summary Send Payment Email
 */
export const useSendPaymentEmail = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof sendPaymentEmail>>,
    TError,
    Key,
    SendEmailRequest,
    Awaited<ReturnType<typeof sendPaymentEmail>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getSendPaymentEmailMutationKey();
  const swrFn = getSendPaymentEmailMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
