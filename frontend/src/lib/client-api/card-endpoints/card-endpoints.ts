/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { RegisterCardRequest, UpdateCardRequest } from '../client-shop-api.schemas';
import type { Arguments, Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Register Card
 */
export const registerCard = (registerCardRequest: RegisterCardRequest) => {
  return customClientInstance<void>({
    url: `/cards`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: registerCardRequest,
  });
};

export const getRegisterCardMutationFetcher = () => {
  return (_: Key, { arg }: { arg: RegisterCardRequest }): Promise<void> => {
    return registerCard(arg);
  };
};
export const getRegisterCardMutationKey = () => [`/cards`] as const;

export type RegisterCardMutationResult = NonNullable<Awaited<ReturnType<typeof registerCard>>>;
export type RegisterCardMutationError = void;

/**
 * @summary Register Card
 */
export const useRegisterCard = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof registerCard>>,
    TError,
    Key,
    RegisterCardRequest,
    Awaited<ReturnType<typeof registerCard>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getRegisterCardMutationKey();
  const swrFn = getRegisterCardMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Fetch Card
 */
export const fetchCard = () => {
  return customClientInstance<void>({ url: `/cards`, method: 'GET' });
};

export const getFetchCardKey = () => [`/cards`] as const;

export type FetchCardQueryResult = NonNullable<Awaited<ReturnType<typeof fetchCard>>>;
export type FetchCardQueryError = void;

/**
 * @summary Fetch Card
 */
export const useFetchCard = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof fetchCard>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getFetchCardKey() : null));
  const swrFn = () => fetchCard();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Card
 */
export const updateCard = (updateCardRequest: UpdateCardRequest) => {
  return customClientInstance<void>({
    url: `/cards/update`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateCardRequest,
  });
};

export const getUpdateCardMutationFetcher = () => {
  return (_: Key, { arg }: { arg: UpdateCardRequest }): Promise<void> => {
    return updateCard(arg);
  };
};
export const getUpdateCardMutationKey = () => [`/cards/update`] as const;

export type UpdateCardMutationResult = NonNullable<Awaited<ReturnType<typeof updateCard>>>;
export type UpdateCardMutationError = void;

/**
 * @summary Update Card
 */
export const useUpdateCard = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof updateCard>>,
    TError,
    Key,
    UpdateCardRequest,
    Awaited<ReturnType<typeof updateCard>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateCardMutationKey();
  const swrFn = getUpdateCardMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Delete Card
 */
export const deleteCard = (cardSequence: number) => {
  return customClientInstance<void>({ url: `/cards/${cardSequence}`, method: 'DELETE' });
};

export const getDeleteCardMutationFetcher = (cardSequence: number) => {
  return (_: Key, __: { arg: Arguments }): Promise<void> => {
    return deleteCard(cardSequence);
  };
};
export const getDeleteCardMutationKey = (cardSequence: number) => [`/cards/${cardSequence}`] as const;

export type DeleteCardMutationResult = NonNullable<Awaited<ReturnType<typeof deleteCard>>>;
export type DeleteCardMutationError = unknown;

/**
 * @summary Delete Card
 */
export const useDeleteCard = <TError = unknown>(
  cardSequence: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof deleteCard>>,
      TError,
      Key,
      Arguments,
      Awaited<ReturnType<typeof deleteCard>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getDeleteCardMutationKey(cardSequence);
  const swrFn = getDeleteCardMutationFetcher(cardSequence);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
