/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  CartItemsResponseBody,
  CheckCartItemPriceRequest,
  CreateCartItemRequest,
  UpdateCartItemRequest,
} from '../client-shop-api.schemas';
import type { Arguments, Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Get Cart Items
 */
export const getCartItems = (creatorAccountIdentity: string) => {
  return customClientInstance<CartItemsResponseBody>({
    url: `/shops/${creatorAccountIdentity}/cart-items`,
    method: 'GET',
  });
};

export const getGetCartItemsKey = (creatorAccountIdentity: string) =>
  [`/shops/${creatorAccountIdentity}/cart-items`] as const;

export type GetCartItemsQueryResult = NonNullable<Awaited<ReturnType<typeof getCartItems>>>;
export type GetCartItemsQueryError = void;

/**
 * @summary Get Cart Items
 */
export const useGetCartItems = <TError = void>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getCartItems>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!creatorAccountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCartItemsKey(creatorAccountIdentity) : null));
  const swrFn = () => getCartItems(creatorAccountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create Cart Item
 */
export const createCartItem = (creatorAccountIdentity: string, createCartItemRequest: CreateCartItemRequest) => {
  return customClientInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createCartItemRequest,
  });
};

export const getCreateCartItemMutationFetcher = (creatorAccountIdentity: string) => {
  return (_: Key, { arg }: { arg: CreateCartItemRequest }): Promise<void> => {
    return createCartItem(creatorAccountIdentity, arg);
  };
};
export const getCreateCartItemMutationKey = (creatorAccountIdentity: string) =>
  [`/shops/${creatorAccountIdentity}/cart-items`] as const;

export type CreateCartItemMutationResult = NonNullable<Awaited<ReturnType<typeof createCartItem>>>;
export type CreateCartItemMutationError = void;

/**
 * @summary Create Cart Item
 */
export const useCreateCartItem = <TError = void>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof createCartItem>>,
      TError,
      Key,
      CreateCartItemRequest,
      Awaited<ReturnType<typeof createCartItem>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateCartItemMutationKey(creatorAccountIdentity);
  const swrFn = getCreateCartItemMutationFetcher(creatorAccountIdentity);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Check Cart Item Price
 */
export const checkCartItemPrice = (
  creatorAccountIdentity: string,
  checkCartItemPriceRequest: CheckCartItemPriceRequest,
) => {
  return customClientInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items/check-price`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: checkCartItemPriceRequest,
  });
};

export const getCheckCartItemPriceMutationFetcher = (creatorAccountIdentity: string) => {
  return (_: Key, { arg }: { arg: CheckCartItemPriceRequest }): Promise<void> => {
    return checkCartItemPrice(creatorAccountIdentity, arg);
  };
};
export const getCheckCartItemPriceMutationKey = (creatorAccountIdentity: string) =>
  [`/shops/${creatorAccountIdentity}/cart-items/check-price`] as const;

export type CheckCartItemPriceMutationResult = NonNullable<Awaited<ReturnType<typeof checkCartItemPrice>>>;
export type CheckCartItemPriceMutationError = void;

/**
 * @summary Check Cart Item Price
 */
export const useCheckCartItemPrice = <TError = void>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof checkCartItemPrice>>,
      TError,
      Key,
      CheckCartItemPriceRequest,
      Awaited<ReturnType<typeof checkCartItemPrice>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCheckCartItemPriceMutationKey(creatorAccountIdentity);
  const swrFn = getCheckCartItemPriceMutationFetcher(creatorAccountIdentity);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Invalid Cart Items
 */
export const getInvalidCartItems = (creatorAccountIdentity: string) => {
  return customClientInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items/invalid-items`,
    method: 'DELETE',
  });
};

export const getGetInvalidCartItemsMutationFetcher = (creatorAccountIdentity: string) => {
  return (_: Key, __: { arg: Arguments }): Promise<void> => {
    return getInvalidCartItems(creatorAccountIdentity);
  };
};
export const getGetInvalidCartItemsMutationKey = (creatorAccountIdentity: string) =>
  [`/shops/${creatorAccountIdentity}/cart-items/invalid-items`] as const;

export type GetInvalidCartItemsMutationResult = NonNullable<Awaited<ReturnType<typeof getInvalidCartItems>>>;
export type GetInvalidCartItemsMutationError = void;

/**
 * @summary Get Invalid Cart Items
 */
export const useGetInvalidCartItems = <TError = void>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof getInvalidCartItems>>,
      TError,
      Key,
      Arguments,
      Awaited<ReturnType<typeof getInvalidCartItems>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getGetInvalidCartItemsMutationKey(creatorAccountIdentity);
  const swrFn = getGetInvalidCartItemsMutationFetcher(creatorAccountIdentity);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Cart Item
 */
export const updateCartItem = (
  creatorAccountIdentity: string,
  cartItemId: number,
  updateCartItemRequest: UpdateCartItemRequest,
) => {
  return customClientInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items/${cartItemId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateCartItemRequest,
  });
};

export const getUpdateCartItemMutationFetcher = (creatorAccountIdentity: string, cartItemId: number) => {
  return (_: Key, { arg }: { arg: UpdateCartItemRequest }): Promise<void> => {
    return updateCartItem(creatorAccountIdentity, cartItemId, arg);
  };
};
export const getUpdateCartItemMutationKey = (creatorAccountIdentity: string, cartItemId: number) =>
  [`/shops/${creatorAccountIdentity}/cart-items/${cartItemId}`] as const;

export type UpdateCartItemMutationResult = NonNullable<Awaited<ReturnType<typeof updateCartItem>>>;
export type UpdateCartItemMutationError = void;

/**
 * @summary Update Cart Item
 */
export const useUpdateCartItem = <TError = void>(
  creatorAccountIdentity: string,
  cartItemId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateCartItem>>,
      TError,
      Key,
      UpdateCartItemRequest,
      Awaited<ReturnType<typeof updateCartItem>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateCartItemMutationKey(creatorAccountIdentity, cartItemId);
  const swrFn = getUpdateCartItemMutationFetcher(creatorAccountIdentity, cartItemId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Delete Cart Item
 */
export const deleteCartItem = (creatorAccountIdentity: string, cartItemId: number) => {
  return customClientInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items/${cartItemId}`,
    method: 'DELETE',
  });
};

export const getDeleteCartItemMutationFetcher = (creatorAccountIdentity: string, cartItemId: number) => {
  return (_: Key, __: { arg: Arguments }): Promise<void> => {
    return deleteCartItem(creatorAccountIdentity, cartItemId);
  };
};
export const getDeleteCartItemMutationKey = (creatorAccountIdentity: string, cartItemId: number) =>
  [`/shops/${creatorAccountIdentity}/cart-items/${cartItemId}`] as const;

export type DeleteCartItemMutationResult = NonNullable<Awaited<ReturnType<typeof deleteCartItem>>>;
export type DeleteCartItemMutationError = void;

/**
 * @summary Delete Cart Item
 */
export const useDeleteCartItem = <TError = void>(
  creatorAccountIdentity: string,
  cartItemId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof deleteCartItem>>,
      TError,
      Key,
      Arguments,
      Awaited<ReturnType<typeof deleteCartItem>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getDeleteCartItemMutationKey(creatorAccountIdentity, cartItemId);
  const swrFn = getDeleteCartItemMutationFetcher(creatorAccountIdentity, cartItemId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
