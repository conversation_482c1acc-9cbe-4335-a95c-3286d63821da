/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { CreateItemPasswordUnlockCacheRequest } from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Get Item Password Unlock Cache
 */
export const getItemPasswordUnlockCache = (creatorAccountIdentity: string, itemId: number) => {
  return customClientInstance<void>({
    url: `/shops/${creatorAccountIdentity}/items/${itemId}/password-unlock`,
    method: 'GET',
  });
};

export const getGetItemPasswordUnlockCacheKey = (creatorAccountIdentity: string, itemId: number) =>
  [`/shops/${creatorAccountIdentity}/items/${itemId}/password-unlock`] as const;

export type GetItemPasswordUnlockCacheQueryResult = NonNullable<Awaited<ReturnType<typeof getItemPasswordUnlockCache>>>;
export type GetItemPasswordUnlockCacheQueryError = void;

/**
 * @summary Get Item Password Unlock Cache
 */
export const useGetItemPasswordUnlockCache = <TError = void>(
  creatorAccountIdentity: string,
  itemId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getItemPasswordUnlockCache>>, TError> & {
      swrKey?: Key;
      enabled?: boolean;
    };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!(creatorAccountIdentity && itemId);
  const swrKey =
    swrOptions?.swrKey ?? (() => (isEnabled ? getGetItemPasswordUnlockCacheKey(creatorAccountIdentity, itemId) : null));
  const swrFn = () => getItemPasswordUnlockCache(creatorAccountIdentity, itemId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create Item Password Unlock Cache
 */
export const createItemPasswordUnlockCache = (
  creatorAccountIdentity: string,
  itemId: number,
  createItemPasswordUnlockCacheRequest: CreateItemPasswordUnlockCacheRequest,
) => {
  return customClientInstance<void>({
    url: `/shops/${creatorAccountIdentity}/items/${itemId}/password-unlock`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createItemPasswordUnlockCacheRequest,
  });
};

export const getCreateItemPasswordUnlockCacheMutationFetcher = (creatorAccountIdentity: string, itemId: number) => {
  return (_: Key, { arg }: { arg: CreateItemPasswordUnlockCacheRequest }): Promise<void> => {
    return createItemPasswordUnlockCache(creatorAccountIdentity, itemId, arg);
  };
};
export const getCreateItemPasswordUnlockCacheMutationKey = (creatorAccountIdentity: string, itemId: number) =>
  [`/shops/${creatorAccountIdentity}/items/${itemId}/password-unlock`] as const;

export type CreateItemPasswordUnlockCacheMutationResult = NonNullable<
  Awaited<ReturnType<typeof createItemPasswordUnlockCache>>
>;
export type CreateItemPasswordUnlockCacheMutationError = void;

/**
 * @summary Create Item Password Unlock Cache
 */
export const useCreateItemPasswordUnlockCache = <TError = void>(
  creatorAccountIdentity: string,
  itemId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof createItemPasswordUnlockCache>>,
      TError,
      Key,
      CreateItemPasswordUnlockCacheRequest,
      Awaited<ReturnType<typeof createItemPasswordUnlockCache>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateItemPasswordUnlockCacheMutationKey(creatorAccountIdentity, itemId);
  const swrFn = getCreateItemPasswordUnlockCacheMutationFetcher(creatorAccountIdentity, itemId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
