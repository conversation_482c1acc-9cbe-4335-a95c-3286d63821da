/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { Key, SWRConfiguration } from 'swr';

/**
 * @summary Health Check
 */
export const healthCheck = () => {
  return customClientInstance<void>({ url: `/hc`, method: 'GET' });
};

export const getHealthCheckKey = () => [`/hc`] as const;

export type HealthCheckQueryResult = NonNullable<Awaited<ReturnType<typeof healthCheck>>>;
export type HealthCheckQueryError = unknown;

/**
 * @summary Health Check
 */
export const useHealthCheck = <TError = unknown>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof healthCheck>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getHealthCheckKey() : null));
  const swrFn = () => healthCheck();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
