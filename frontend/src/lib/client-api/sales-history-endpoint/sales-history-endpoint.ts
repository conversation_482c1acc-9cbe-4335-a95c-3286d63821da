/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { Key, SWRConfiguration } from 'swr';

/**
 * @summary Get Sales Histories
 */
export const getSalesHistories = () => {
  return customClientInstance<void>({ url: `/shops/current/sales-history`, method: 'GET' });
};

export const getGetSalesHistoriesKey = () => [`/shops/current/sales-history`] as const;

export type GetSalesHistoriesQueryResult = NonNullable<Awaited<ReturnType<typeof getSalesHistories>>>;
export type GetSalesHistoriesQueryError = unknown;

/**
 * @summary Get Sales Histories
 */
export const useGetSalesHistories = <TError = unknown>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getSalesHistories>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetSalesHistoriesKey() : null));
  const swrFn = () => getSalesHistories();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
