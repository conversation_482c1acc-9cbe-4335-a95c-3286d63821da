/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { ShopResponseBody } from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';

/**
 * @summary Get Shop
 */
export const getShop = (creatorAccountIdentity: string) => {
  return customClientInstance<ShopResponseBody>({ url: `/shops/${creatorAccountIdentity}`, method: 'GET' });
};

export const getGetShopKey = (creatorAccountIdentity: string) => [`/shops/${creatorAccountIdentity}`] as const;

export type GetShopQueryResult = NonNullable<Awaited<ReturnType<typeof getShop>>>;
export type GetShopQueryError = void;

/**
 * @summary Get Shop
 */
export const useGetShop = <TError = void>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getShop>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!creatorAccountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetShopKey(creatorAccountIdentity) : null));
  const swrFn = () => getShop(creatorAccountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
