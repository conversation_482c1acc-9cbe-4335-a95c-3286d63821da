/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type { RankingEventInfoResponseBody } from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';

/**
 * @summary Get Creator Active
 */
export const getCreatorActive = (creatorAccountIdentity: string) => {
  return customClientInstance<RankingEventInfoResponseBody | void>({
    url: `/shops/ranking_event_info/creator/${creatorAccountIdentity}/active`,
    method: 'GET',
  });
};

export const getGetCreatorActiveKey = (creatorAccountIdentity: string) =>
  [`/shops/ranking_event_info/creator/${creatorAccountIdentity}/active`] as const;

export type GetCreatorActiveQueryResult = NonNullable<Awaited<ReturnType<typeof getCreatorActive>>>;
export type GetCreatorActiveQueryError = void;

/**
 * @summary Get Creator Active
 */
export const useGetCreatorActive = <TError = void>(
  creatorAccountIdentity: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getCreatorActive>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!creatorAccountIdentity;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCreatorActiveKey(creatorAccountIdentity) : null));
  const swrFn = () => getCreatorActive(creatorAccountIdentity);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
