/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';

/**
 * @summary Get Digital Fan Letter Link
 */
export const getDigitalFanLetterLink = (creatorAccountIdentity: string) => {
  return customServerInstance<void>({
    url: `/shops/${creatorAccountIdentity}/seller-apps/digital-fan-letter-link`,
    method: 'GET',
  });
};
export type GetDigitalFanLetterLinkResult = NonNullable<Awaited<ReturnType<typeof getDigitalFanLetterLink>>>;
