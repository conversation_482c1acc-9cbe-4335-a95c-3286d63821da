/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';

/**
 * @summary Get Sales Histories
 */
export const getSalesHistories = () => {
  return customServerInstance<void>({ url: `/shops/current/sales-history`, method: 'GET' });
};
export type GetSalesHistoriesResult = NonNullable<Awaited<ReturnType<typeof getSalesHistories>>>;
