/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { RegisterCardRequest, UpdateCardRequest } from '../shop-api.schemas';

/**
 * @summary Register Card
 */
export const registerCard = (registerCardRequest: RegisterCardRequest) => {
  return customServerInstance<void>({
    url: `/cards`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: registerCardRequest,
  });
};
/**
 * @summary Fetch Card
 */
export const fetchCard = () => {
  return customServerInstance<void>({ url: `/cards`, method: 'GET' });
};
/**
 * @summary Update Card
 */
export const updateCard = (updateCardRequest: UpdateCardRequest) => {
  return customServerInstance<void>({
    url: `/cards/update`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateCardRequest,
  });
};
/**
 * @summary Delete Card
 */
export const deleteCard = (cardSequence: number) => {
  return customServerInstance<void>({ url: `/cards/${cardSequence}`, method: 'DELETE' });
};
export type RegisterCardResult = NonNullable<Awaited<ReturnType<typeof registerCard>>>;
export type FetchCardResult = NonNullable<Awaited<ReturnType<typeof fetchCard>>>;
export type UpdateCardResult = NonNullable<Awaited<ReturnType<typeof updateCard>>>;
export type DeleteCardResult = NonNullable<Awaited<ReturnType<typeof deleteCard>>>;
