/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { GetPurchasedItemsParams, PurchaseItemResponseBody } from '../shop-api.schemas';

/**
 * @summary Get Purchased Items
 */
export const getPurchasedItems = (params?: GetPurchasedItemsParams) => {
  return customServerInstance<void>({ url: `/shops/current/purchased-items`, method: 'GET', params });
};
/**
 * @summary Get Purchased Item
 */
export const getPurchasedItem = (purchasedItemId: number) => {
  return customServerInstance<PurchaseItemResponseBody>({
    url: `/shops/current/purchased-items/${purchasedItemId}`,
    method: 'GET',
  });
};
export type GetPurchasedItemsResult = NonNullable<Awaited<ReturnType<typeof getPurchasedItems>>>;
export type GetPurchasedItemResult = NonNullable<Awaited<ReturnType<typeof getPurchasedItem>>>;
