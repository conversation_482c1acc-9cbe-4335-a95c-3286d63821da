/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  CreateDigitalGachaItemRequest,
  ItemResponseBody,
  UpdateDigitalGachaItemRequest,
} from '../shop-api.schemas';

/**
 * @summary Create Digital Gacha Item
 */
export const createDigitalGachaItem = (createDigitalGachaItemRequest: CreateDigitalGachaItemRequest) => {
  return customServerInstance<ItemResponseBody>({
    url: `/shops/current/digital-gacha`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createDigitalGachaItemRequest,
  });
};
/**
 * @summary Update Digital Gacha Item
 */
export const updateDigitalGachaItem = (
  itemId: number,
  updateDigitalGachaItemRequest: UpdateDigitalGachaItemRequest,
) => {
  return customServerInstance<ItemResponseBody>({
    url: `/shops/current/digital-gacha/${itemId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateDigitalGachaItemRequest,
  });
};
export type CreateDigitalGachaItemResult = NonNullable<Awaited<ReturnType<typeof createDigitalGachaItem>>>;
export type UpdateDigitalGachaItemResult = NonNullable<Awaited<ReturnType<typeof updateDigitalGachaItem>>>;
