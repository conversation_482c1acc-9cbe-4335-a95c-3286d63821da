/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { GetItemsParams, ItemResponseBody } from '../shop-api.schemas';

/**
 * @summary Get Items
 */
export const getItems = (creatorAccountIdentity: string, params?: GetItemsParams) => {
  return customServerInstance<void>({ url: `/shops/${creatorAccountIdentity}/items`, method: 'GET', params });
};
/**
 * @summary Get Item
 */
export const getItem = (creatorAccountIdentity: string, itemId: number) => {
  return customServerInstance<ItemResponseBody>({
    url: `/shops/${creatorAccountIdentity}/items/${itemId}`,
    method: 'GET',
  });
};
export type GetItemsResult = NonNullable<Awaited<ReturnType<typeof getItems>>>;
export type GetItemResult = NonNullable<Awaited<ReturnType<typeof getItem>>>;
