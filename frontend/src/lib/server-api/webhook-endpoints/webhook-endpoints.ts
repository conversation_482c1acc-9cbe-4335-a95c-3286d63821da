/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { GmoWebhookBody } from '../shop-api.schemas';

/**
 * @summary Gmo Webhook
 */
export const gmoWebhook = (gmoWebhookBody: GmoWebhookBody) => {
  const formUrlEncoded = new URLSearchParams();
  formUrlEncoded.append(`ShopID`, gmoWebhookBody.ShopID);
  formUrlEncoded.append(`ShopPass`, gmoWebhookBody.ShopPass);
  formUrlEncoded.append(`AccessID`, gmoWebhookBody.AccessID);
  formUrlEncoded.append(`AccessPass`, gmoWebhookBody.AccessPass);
  formUrlEncoded.append(`OrderID`, gmoWebhookBody.OrderID);
  formUrlEncoded.append(`Status`, gmoWebhookBody.Status);
  formUrlEncoded.append(`Amount`, gmoWebhookBody.Amount);
  formUrlEncoded.append(`Tax`, gmoWebhookBody.Tax);
  formUrlEncoded.append(`PayType`, gmoWebhookBody.PayType);

  return customServerInstance<void>({
    url: `/webhook/gmo/payment/notice`,
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: formUrlEncoded,
  });
};
export type GmoWebhookResult = NonNullable<Awaited<ReturnType<typeof gmoWebhook>>>;
