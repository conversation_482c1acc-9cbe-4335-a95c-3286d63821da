/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { ShopResponseBody } from '../shop-api.schemas';

/**
 * @summary Get Shop
 */
export const getShop = (creatorAccountIdentity: string) => {
  return customServerInstance<ShopResponseBody>({ url: `/shops/${creatorAccountIdentity}`, method: 'GET' });
};
export type GetShopResult = NonNullable<Awaited<ReturnType<typeof getShop>>>;
