/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  CreateOrUpdateItemRequest,
  GetCurrentUserItemParams,
  GetCurrentUserItemsParams,
  ItemResponseBody,
  SortItemsRequest,
} from '../shop-api.schemas';

/**
 * @summary Get Items
 */
export const getCurrentUserItems = (params?: GetCurrentUserItemsParams) => {
  return customServerInstance<void>({ url: `/shops/current/items`, method: 'GET', params });
};
/**
 * @summary Create Item
 */
export const createItem = (createOrUpdateItemRequest: CreateOrUpdateItemRequest) => {
  return customServerInstance<void>({
    url: `/shops/current/items`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createOrUpdateItemRequest,
  });
};
/**
 * @summary Sort Items
 */
export const sortItems = (sortItemsRequest: SortItemsRequest) => {
  return customServerInstance<void>({
    url: `/shops/current/items/sort`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: sortItemsRequest,
  });
};
/**
 * @summary Update Item
 */
export const updateItem = (itemId: number, createOrUpdateItemRequest: CreateOrUpdateItemRequest) => {
  return customServerInstance<void>({
    url: `/shops/current/items/${itemId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: createOrUpdateItemRequest,
  });
};
/**
 * @summary Get Item
 */
export const getCurrentUserItem = (itemId: number, params?: GetCurrentUserItemParams) => {
  return customServerInstance<ItemResponseBody>({ url: `/shops/current/items/${itemId}`, method: 'GET', params });
};
export type GetCurrentUserItemsResult = NonNullable<Awaited<ReturnType<typeof getCurrentUserItems>>>;
export type CreateItemResult = NonNullable<Awaited<ReturnType<typeof createItem>>>;
export type SortItemsResult = NonNullable<Awaited<ReturnType<typeof sortItems>>>;
export type UpdateItemResult = NonNullable<Awaited<ReturnType<typeof updateItem>>>;
export type GetCurrentUserItemResult = NonNullable<Awaited<ReturnType<typeof getCurrentUserItem>>>;
