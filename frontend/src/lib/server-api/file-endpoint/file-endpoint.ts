/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  DownloadUrlResponseBody,
  GetDownloadUrlRequest,
  GetPreSignedUrlRequest,
  GetUploadUrlRequest,
} from '../shop-api.schemas';

/**
 * @summary Get Download Url
 */
export const getDownloadUrl = (getDownloadUrlRequest: GetDownloadUrlRequest) => {
  return customServerInstance<DownloadUrlResponseBody>({
    url: `/shops/current/files/download-url`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: getDownloadUrlRequest,
  });
};
/**
 * @summary Get Pre Signed Url
 */
export const getPreSignedUrl = (getPreSignedUrlRequest: GetPreSignedUrlRequest) => {
  return customServerInstance<void>({
    url: `/shops/current/files/presigned-url`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: getPreSignedUrlRequest,
  });
};
/**
 * @summary Get Upload Url
 */
export const getUploadUrl = (getUploadUrlRequest: GetUploadUrlRequest) => {
  return customServerInstance<void>({
    url: `/shops/current/files/upload-url`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: getUploadUrlRequest,
  });
};
export type GetDownloadUrlResult = NonNullable<Awaited<ReturnType<typeof getDownloadUrl>>>;
export type GetPreSignedUrlResult = NonNullable<Awaited<ReturnType<typeof getPreSignedUrl>>>;
export type GetUploadUrlResult = NonNullable<Awaited<ReturnType<typeof getUploadUrl>>>;
