/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  CartItemsResponseBody,
  CheckCartItemPriceRequest,
  CreateCartItemRequest,
  UpdateCartItemRequest,
} from '../shop-api.schemas';

/**
 * @summary Get Cart Items
 */
export const getCartItems = (creatorAccountIdentity: string) => {
  return customServerInstance<CartItemsResponseBody>({
    url: `/shops/${creatorAccountIdentity}/cart-items`,
    method: 'GET',
  });
};
/**
 * @summary Create Cart Item
 */
export const createCartItem = (creatorAccountIdentity: string, createCartItemRequest: CreateCartItemRequest) => {
  return customServerInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createCartItemRequest,
  });
};
/**
 * @summary Check Cart Item Price
 */
export const checkCartItemPrice = (
  creatorAccountIdentity: string,
  checkCartItemPriceRequest: CheckCartItemPriceRequest,
) => {
  return customServerInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items/check-price`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: checkCartItemPriceRequest,
  });
};
/**
 * @summary Get Invalid Cart Items
 */
export const getInvalidCartItems = (creatorAccountIdentity: string) => {
  return customServerInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items/invalid-items`,
    method: 'DELETE',
  });
};
/**
 * @summary Update Cart Item
 */
export const updateCartItem = (
  creatorAccountIdentity: string,
  cartItemId: number,
  updateCartItemRequest: UpdateCartItemRequest,
) => {
  return customServerInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items/${cartItemId}`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateCartItemRequest,
  });
};
/**
 * @summary Delete Cart Item
 */
export const deleteCartItem = (creatorAccountIdentity: string, cartItemId: number) => {
  return customServerInstance<void>({
    url: `/shops/${creatorAccountIdentity}/cart-items/${cartItemId}`,
    method: 'DELETE',
  });
};
export type GetCartItemsResult = NonNullable<Awaited<ReturnType<typeof getCartItems>>>;
export type CreateCartItemResult = NonNullable<Awaited<ReturnType<typeof createCartItem>>>;
export type CheckCartItemPriceResult = NonNullable<Awaited<ReturnType<typeof checkCartItemPrice>>>;
export type GetInvalidCartItemsResult = NonNullable<Awaited<ReturnType<typeof getInvalidCartItems>>>;
export type UpdateCartItemResult = NonNullable<Awaited<ReturnType<typeof updateCartItem>>>;
export type DeleteCartItemResult = NonNullable<Awaited<ReturnType<typeof deleteCartItem>>>;
