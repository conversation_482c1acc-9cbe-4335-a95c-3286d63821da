/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { SendEmailRequest } from '../shop-api.schemas';

/**
 * @summary Send Payment Email
 */
export const sendPaymentEmail = (sendEmailRequest: SendEmailRequest) => {
  return customServerInstance<void>({
    url: `/email/payment`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: sendEmailRequest,
  });
};
export type SendPaymentEmailResult = NonNullable<Awaited<ReturnType<typeof sendPaymentEmail>>>;
