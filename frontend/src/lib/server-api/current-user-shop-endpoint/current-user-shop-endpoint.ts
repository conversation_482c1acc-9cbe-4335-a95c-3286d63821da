/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { CreateShopRequest, UpdateShopRequest } from '../shop-api.schemas';

/**
 * @summary Update Shop
 */
export const updateShop = (updateShopRequest: UpdateShopRequest) => {
  return customServerInstance<void>({
    url: `/shops/current`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateShopRequest,
  });
};
/**
 * @summary Get Shop
 */
export const getCurrentUserShop = () => {
  return customServerInstance<void>({ url: `/shops/current`, method: 'GET' });
};
/**
 * @summary Create Shop
 */
export const createShop = (createShopRequest: CreateShopRequest) => {
  return customServerInstance<void>({
    url: `/shops/current`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createShopRequest,
  });
};
export type UpdateShopResult = NonNullable<Awaited<ReturnType<typeof updateShop>>>;
export type GetCurrentUserShopResult = NonNullable<Awaited<ReturnType<typeof getCurrentUserShop>>>;
export type CreateShopResult = NonNullable<Awaited<ReturnType<typeof createShop>>>;
