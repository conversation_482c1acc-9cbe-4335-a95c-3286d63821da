/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { RankingEventInfoResponseBody } from '../shop-api.schemas';

/**
 * @summary Get Creator Active
 */
export const getCreatorActive = (creatorAccountIdentity: string) => {
  return customServerInstance<RankingEventInfoResponseBody | void>({
    url: `/shops/ranking_event_info/creator/${creatorAccountIdentity}/active`,
    method: 'GET',
  });
};
export type GetCreatorActiveResult = NonNullable<Awaited<ReturnType<typeof getCreatorActive>>>;
