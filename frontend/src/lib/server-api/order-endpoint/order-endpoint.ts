/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { FinalizeCreditCard3DSecureRequest, TipLimitResponseBody, UpdateOrderRequest } from '../shop-api.schemas';

/**
 * @summary Update Order
 */
export const updateOrder = (updateOrderRequest: UpdateOrderRequest) => {
  return customServerInstance<void>({
    url: `/orders`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateOrderRequest,
  });
};
/**
 * @summary Get Orders
 */
export const getOrders = () => {
  return customServerInstance<void>({ url: `/orders`, method: 'GET' });
};
/**
 * @summary Get Convenience Fees
 */
export const getConvenienceFees = () => {
  return customServerInstance<void>({ url: `/orders/convenience-fees`, method: 'GET' });
};
/**
 * @summary Finalize Credit Card 3 D Secure
 */
export const finalizeCreditCard3DSecure = (finalizeCreditCard3DSecureRequest: FinalizeCreditCard3DSecureRequest) => {
  return customServerInstance<void>({
    url: `/orders/finalize-credit-card-3d-secure`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: finalizeCreditCard3DSecureRequest,
  });
};
/**
 * @summary Get Tip Limit
 */
export const getTipLimit = (creatorAccountIdentity: string) => {
  return customServerInstance<TipLimitResponseBody>({
    url: `/orders/tip-upper-limit/${creatorAccountIdentity}`,
    method: 'GET',
  });
};
export type UpdateOrderResult = NonNullable<Awaited<ReturnType<typeof updateOrder>>>;
export type GetOrdersResult = NonNullable<Awaited<ReturnType<typeof getOrders>>>;
export type GetConvenienceFeesResult = NonNullable<Awaited<ReturnType<typeof getConvenienceFees>>>;
export type FinalizeCreditCard3DSecureResult = NonNullable<Awaited<ReturnType<typeof finalizeCreditCard3DSecure>>>;
export type GetTipLimitResult = NonNullable<Awaited<ReturnType<typeof getTipLimit>>>;
