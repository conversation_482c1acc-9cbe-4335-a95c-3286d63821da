/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type { CreateItemPasswordUnlockCacheRequest } from '../shop-api.schemas';

/**
 * @summary Get Item Password Unlock Cache
 */
export const getItemPasswordUnlockCache = (creatorAccountIdentity: string, itemId: number) => {
  return customServerInstance<void>({
    url: `/shops/${creatorAccountIdentity}/items/${itemId}/password-unlock`,
    method: 'GET',
  });
};
/**
 * @summary Create Item Password Unlock Cache
 */
export const createItemPasswordUnlockCache = (
  creatorAccountIdentity: string,
  itemId: number,
  createItemPasswordUnlockCacheRequest: CreateItemPasswordUnlockCacheRequest,
) => {
  return customServerInstance<void>({
    url: `/shops/${creatorAccountIdentity}/items/${itemId}/password-unlock`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createItemPasswordUnlockCacheRequest,
  });
};
export type GetItemPasswordUnlockCacheResult = NonNullable<Awaited<ReturnType<typeof getItemPasswordUnlockCache>>>;
export type CreateItemPasswordUnlockCacheResult = NonNullable<
  Awaited<ReturnType<typeof createItemPasswordUnlockCache>>
>;
