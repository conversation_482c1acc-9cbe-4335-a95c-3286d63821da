/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';
import type {
  AgenciesResponseBody,
  AgencySalesResponseBody,
  ConsoleUsersResponseBody,
  GetAgencySalesParams,
  UsersResponseBody,
} from '../shop-api.schemas';

/**
 * @summary Get Agencies
 */
export const getAgencies = () => {
  return customServerInstance<AgenciesResponseBody>({ url: `/console/agencies`, method: 'GET' });
};
/**
 * @summary Get Agency Sales
 */
export const getAgencySales = (agencyId: string, params?: GetAgencySalesParams) => {
  return customServerInstance<AgencySalesResponseBody>({
    url: `/console/agencies/${agencyId}/sales`,
    method: 'GET',
    params,
  });
};
/**
 * @summary Get Agency Users
 */
export const getAgencyUsers = (agencyId: string) => {
  return customServerInstance<UsersResponseBody>({ url: `/console/agencies/${agencyId}/users`, method: 'GET' });
};
/**
 * @summary Get Console Users
 */
export const getConsoleUsers = () => {
  return customServerInstance<ConsoleUsersResponseBody>({ url: `/console/users`, method: 'GET' });
};
export type GetAgenciesResult = NonNullable<Awaited<ReturnType<typeof getAgencies>>>;
export type GetAgencySalesResult = NonNullable<Awaited<ReturnType<typeof getAgencySales>>>;
export type GetAgencyUsersResult = NonNullable<Awaited<ReturnType<typeof getAgencyUsers>>>;
export type GetConsoleUsersResult = NonNullable<Awaited<ReturnType<typeof getConsoleUsers>>>;
