/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import { customServerInstance } from '../../../../config/custom-server-instance';

/**
 * @summary Health Check
 */
export const healthCheck = () => {
  return customServerInstance<void>({ url: `/hc`, method: 'GET' });
};
export type HealthCheckResult = NonNullable<Awaited<ReturnType<typeof healthCheck>>>;
