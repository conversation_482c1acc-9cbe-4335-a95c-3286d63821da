/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customFanmeInstance } from '../../../../config/custom-fanme-instance';
import type {
  CallbackParams,
  CreateContentWithDetailRequest,
  FanmeCustomerResponseBody,
  FanmeParams,
  SaveFanmeCustomerRequest,
  SuggestAddressResponseBody,
} from '../fanme-api.schemas';
import type { Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Suggest Address
 */
export const suggestAddress = (postalCode: string) => {
  return customFanmeInstance<SuggestAddressResponseBody>({
    url: `/address/suggestion/postal-code/${postalCode}`,
    method: 'GET',
  });
};

export const getSuggestAddressKey = (postalCode: string) => [`/address/suggestion/postal-code/${postalCode}`] as const;

export type SuggestAddressQueryResult = NonNullable<Awaited<ReturnType<typeof suggestAddress>>>;
export type SuggestAddressQueryError = void;

/**
 * @summary Suggest Address
 */
export const useSuggestAddress = <TError = void>(
  postalCode: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof suggestAddress>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!postalCode;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getSuggestAddressKey(postalCode) : null));
  const swrFn = () => suggestAddress(postalCode);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * OAuth認証を開始し、認証プロバイダへのリダイレクト先URLを取得するエンドポイント
 * @summary Fanme OAuth認証開始
 */
export const fanme = (params?: FanmeParams) => {
  return customFanmeInstance<void>({ url: `/auth/fanme`, method: 'GET', params });
};

export const getFanmeKey = (params?: FanmeParams) => [`/auth/fanme`, ...(params ? [params] : [])] as const;

export type FanmeQueryResult = NonNullable<Awaited<ReturnType<typeof fanme>>>;
export type FanmeQueryError = unknown;

/**
 * @summary Fanme OAuth認証開始
 */
export const useFanme = <TError = unknown>(
  params?: FanmeParams,
  options?: { swr?: SWRConfiguration<Awaited<ReturnType<typeof fanme>>, TError> & { swrKey?: Key; enabled?: boolean } },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getFanmeKey(params) : null));
  const swrFn = () => fanme(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * OAuthプロバイダからのコールバックを受け取り、認証処理を実行する
 * @summary OAuthコールバック処理
 */
export const callback = (params: CallbackParams) => {
  return customFanmeInstance<void>({ url: `/auth/fanme/callback`, method: 'GET', params });
};

export const getCallbackKey = (params: CallbackParams) =>
  [`/auth/fanme/callback`, ...(params ? [params] : [])] as const;

export type CallbackQueryResult = NonNullable<Awaited<ReturnType<typeof callback>>>;
export type CallbackQueryError = void;

/**
 * @summary OAuthコールバック処理
 */
export const useCallback = <TError = void>(
  params: CallbackParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof callback>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getCallbackKey(params) : null));
  const swrFn = () => callback(params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Create With Detail
 */
export const createWithDetail = (createContentWithDetailRequest: CreateContentWithDetailRequest) => {
  return customFanmeInstance<void>({
    url: `/fanme/content_blocks/create_with_detail`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createContentWithDetailRequest,
  });
};

export const getCreateWithDetailMutationFetcher = () => {
  return (_: Key, { arg }: { arg: CreateContentWithDetailRequest }): Promise<void> => {
    return createWithDetail(arg);
  };
};
export const getCreateWithDetailMutationKey = () => [`/fanme/content_blocks/create_with_detail`] as const;

export type CreateWithDetailMutationResult = NonNullable<Awaited<ReturnType<typeof createWithDetail>>>;
export type CreateWithDetailMutationError = void;

/**
 * @summary Create With Detail
 */
export const useCreateWithDetail = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof createWithDetail>>,
    TError,
    Key,
    CreateContentWithDetailRequest,
    Awaited<ReturnType<typeof createWithDetail>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getCreateWithDetailMutationKey();
  const swrFn = getCreateWithDetailMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Save Fanme Customer
 */
export const saveFanmeCustomer = (saveFanmeCustomerRequest: SaveFanmeCustomerRequest) => {
  return customFanmeInstance<void>({
    url: `/fanme/fanme-customers`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: saveFanmeCustomerRequest,
  });
};

export const getSaveFanmeCustomerMutationFetcher = () => {
  return (_: Key, { arg }: { arg: SaveFanmeCustomerRequest }): Promise<void> => {
    return saveFanmeCustomer(arg);
  };
};
export const getSaveFanmeCustomerMutationKey = () => [`/fanme/fanme-customers`] as const;

export type SaveFanmeCustomerMutationResult = NonNullable<Awaited<ReturnType<typeof saveFanmeCustomer>>>;
export type SaveFanmeCustomerMutationError = void;

/**
 * @summary Save Fanme Customer
 */
export const useSaveFanmeCustomer = <TError = void>(options?: {
  swr?: SWRMutationConfiguration<
    Awaited<ReturnType<typeof saveFanmeCustomer>>,
    TError,
    Key,
    SaveFanmeCustomerRequest,
    Awaited<ReturnType<typeof saveFanmeCustomer>>
  > & { swrKey?: string };
}) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getSaveFanmeCustomerMutationKey();
  const swrFn = getSaveFanmeCustomerMutationFetcher();

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Fanme Customer
 */
export const getFanmeCustomer = () => {
  return customFanmeInstance<FanmeCustomerResponseBody>({ url: `/fanme/fanme-customers`, method: 'GET' });
};

export const getGetFanmeCustomerKey = () => [`/fanme/fanme-customers`] as const;

export type GetFanmeCustomerQueryResult = NonNullable<Awaited<ReturnType<typeof getFanmeCustomer>>>;
export type GetFanmeCustomerQueryError = void;

/**
 * @summary Get Fanme Customer
 */
export const useGetFanmeCustomer = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getFanmeCustomer>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetFanmeCustomerKey() : null));
  const swrFn = () => getFanmeCustomer();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Current User
 */
export const getCurrentUser = () => {
  return customFanmeInstance<unknown>({ url: `/fanme/users/current`, method: 'GET' });
};

export const getGetCurrentUserKey = () => [`/fanme/users/current`] as const;

export type GetCurrentUserQueryResult = NonNullable<Awaited<ReturnType<typeof getCurrentUser>>>;
export type GetCurrentUserQueryError = void;

/**
 * @summary Get Current User
 */
export const useGetCurrentUser = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getCurrentUser>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetCurrentUserKey() : null));
  const swrFn = () => getCurrentUser();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Id Token
 */
export const getIdToken = () => {
  return customFanmeInstance<unknown>({ url: `/fanme/users/id_token`, method: 'GET' });
};

export const getGetIdTokenKey = () => [`/fanme/users/id_token`] as const;

export type GetIdTokenQueryResult = NonNullable<Awaited<ReturnType<typeof getIdToken>>>;
export type GetIdTokenQueryError = unknown;

/**
 * @summary Get Id Token
 */
export const useGetIdToken = <TError = unknown>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getIdToken>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetIdTokenKey() : null));
  const swrFn = () => getIdToken();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get User
 */
export const getUser = (userUuid: string) => {
  return customFanmeInstance<unknown>({ url: `/fanme/users/${userUuid}`, method: 'GET' });
};

export const getGetUserKey = (userUuid: string) => [`/fanme/users/${userUuid}`] as const;

export type GetUserQueryResult = NonNullable<Awaited<ReturnType<typeof getUser>>>;
export type GetUserQueryError = unknown;

/**
 * @summary Get User
 */
export const useGetUser = <TError = unknown>(
  userUuid: string,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getUser>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!userUuid;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetUserKey(userUuid) : null));
  const swrFn = () => getUser(userUuid);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
