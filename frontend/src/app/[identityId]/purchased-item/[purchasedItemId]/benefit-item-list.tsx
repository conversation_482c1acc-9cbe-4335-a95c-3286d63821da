'use client';

import React from 'react';
import ShopPublicImage from '@/components/ShopImage';
import { BenefitDataFiles, File } from '@/lib/server-api/shop-api.schemas';
import { usePlayerStore, PlayerType } from '@/store/usePlayer';

interface BenefitItemListProps {
  items: BenefitDataFiles;
  readOnly?: boolean;
  onDownload?: (file: File) => void;
}

const BenefitItemList = ({ items, readOnly = true, onDownload }: BenefitItemListProps) => {
  const { setPlayerProps, onPlayerOpen } = usePlayerStore();

  const getIconPath = (type: string) => {
    switch (type) {
      case 'image':
        return '/images/icons/PhotoIcnWhite.svg';
      case 'audio':
        return '/images/icons/VoiceIcnWhite.svg';
      case 'video':
        return '/images/icons/MovieIcnWhite.svg';
      default:
        return '/images/icons/PhotoIcnWhite.svg';
    }
  };

  const handleClick = (id?: string) => {
    if (readOnly || !id || !items) return;
    if (!id) return;
    const item = items.find((item) => item.id?.toString() === id);
    if (!item) return;
    onPlayerOpen();
    setPlayerProps({
      src: item.objectUri!,
      thumbnail: item.thumbnailUri!,
      type: item.fileType as PlayerType,
    });
  };

  return (
    <div className="flex flex-col items-center gap-0.5">
      {items?.map((item, index) => (
        <div
          key={item.id}
          className={`flex h-10 w-full items-center justify-start gap-2 px-4 ${
            items.length === 1
              ? 'rounded-m bg-gray-700'
              : index === 0
                ? 'rounded-t-m bg-gray-700'
                : index === items.length - 1
                  ? 'rounded-b-m bg-gray-700'
                  : 'bg-gray-700'
          }`}
          onClick={() => !readOnly && handleClick(item.id?.toString())}
        >
          <ShopPublicImage src={getIconPath(item.fileType || 'image')} alt="セット購入特典" width={20} height={20} />
          <div className="text-regular-13 text-white">{item.name}</div>
          {!readOnly && onDownload && (
            <div
              className="ml-auto cursor-pointer"
              onClick={(event: React.MouseEvent) => {
                event.stopPropagation();
                onDownload(item);
              }}
            >
              <ShopPublicImage src="/images/icons/Download_White.svg" alt="download" width={20} height={20} />
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default BenefitItemList;
