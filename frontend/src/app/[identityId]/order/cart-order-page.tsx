'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import CustomToast from '@/components/atoms/toast/custom-toast';
import SectionTitle from '@/components/atoms/typography/section-title';
import { CartStatusError } from '@/components/cart/CartStatusError';
import CartItemList from '@/components/containers/CartItem/CartItemList';
import { useGetTipLimit } from '@/lib/client-api/order-endpoint/order-endpoint';
import { useGetFanmeCustomer } from '@/lib/fanme-api/fanme/fanme';
import { useCurrentUser } from '@/store/useCurrentUser';
import { useModalsStore } from '@/store/useModals';
import { useOrderFormStore } from '@/store/useOrderFormStore';
import OrderPageTemplate from './_components/templates/order-page-template';
import AddressSection from '@/app/[identityId]/order/address-section';
import { STATUS_ERROR } from '@/consts/status';
import { useGetCart } from '@/hooks/swr/useGetCart';
import { useGetConvenienceFees } from '@/hooks/swr/useGetConvenienceFees';
import { useCartOrderProcess } from '@/hooks/use-cart-order-process';
import { useShopCreatorEventInfo } from '@/hooks/use-shop-creator-event-info';
import { useCheckCartItemsStatus } from '@/hooks/useCheckCartItemsStatus';
import { getUserIdentityId } from '@/utils/base';
import { calculateCartItemsTotalPrice, calculateFee, containsPhysicalItem } from '@/utils/cart';
import { createCartOrderFormSchema, OrderFormData } from '@/types/order';

const CartOrderPage = () => {
  const router = useRouter();
  const params = useParams();
  const { currentUser } = useCurrentUser();
  const { onModalClose, onModalOpen, setModalProps } = useModalsStore();
  const searchParams = useSearchParams();

  const { tip, paymentMethod, convenienceParam, selectedCard, clearOtherPaymentParams, setConvenienceResult } =
    useOrderFormStore();

  const identityId = getUserIdentityId(params.identityId as string);

  const { data: cartData } = useGetCart(identityId, currentUser?.accountIdentity as string);
  const { data: tipLimitData } = useGetTipLimit(identityId);
  const { data: convenienceFeesData } = useGetConvenienceFees();
  const { data: fanmeCustomerDate } = useGetFanmeCustomer();

  const cartItems = cartData?.data?.items;
  const isLocked = cartData?.data?.isLocked;
  const tipLimit = tipLimitData?.data?.tipLimit?.amount;
  const convenienceFees = convenienceFeesData?.data || [];
  const fanmeCustomer = fanmeCustomerDate?.data?.fanmeCustomer;

  const cartItemsTotalPrice = calculateCartItemsTotalPrice(cartItems);
  const { yellCount, boostRatio, hasShopCreatorActiveEvent } = useShopCreatorEventInfo(identityId, cartItemsTotalPrice);

  const dialogTextBody = useCallback(
    () => (
      <div className="grid items-center justify-center p-6 text-center">
        <p className="text-lg text-red-500">ただいま他の画面で決済手続き中です</p>
        <p> PayPayで決済を完了してください。</p>
        <p>５分以内に決済が行われていない場合、</p>
        自動的に決済をキャンセルします。
      </div>
    ),
    [],
  );

  const handleOpenDialogWithMark = useCallback(() => {
    onModalOpen();
    setModalProps({
      onClose: () => {
        onModalClose();
        router.push(`/${identityId}/cart`);
      },
      type: 'error',
      children: dialogTextBody(),
    });
  }, [onModalOpen, setModalProps, onModalClose, router, identityId, dialogTextBody]);

  useEffect(() => {
    if (!cartItems) {
      return;
    }

    if (cartData && cartItems.length === 0) {
      router.push(`/${identityId}/cart`);
    }
    if (isLocked) {
      handleOpenDialogWithMark();
      return;
    }
  }, [cartData, cartItems, identityId, router, isLocked, handleOpenDialogWithMark]);

  const cartId = cartItems?.[0]?.cartId;
  const cartItemIds = cartItems?.map((item) => item.cartItemId) || [];

  const fee = calculateFee(paymentMethod, cartItemsTotalPrice, tip, convenienceFees);
  const totalAmount = cartItemsTotalPrice + tip + fee;

  const hasPhysicalItem = useMemo(() => containsPhysicalItem(cartItems), [cartItems]);
  const [schema, setSchema] = useState(createCartOrderFormSchema(false));

  // TODO hooksに移行
  const {
    formState: { isValid },
    trigger,
    setValue,
    reset,
  } = useForm<OrderFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      paymentMethod: paymentMethod as OrderFormData['paymentMethod'],
      tip: tip,
      selectedCard: selectedCard,
      convenienceParam: convenienceParam,
      address: fanmeCustomer?.prefecture ?? '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (searchParams.get('status') === STATUS_ERROR && cartData !== undefined) {
      toast.custom((t) => CustomToast(t, STATUS_ERROR, '決済に失敗しました'));
    }
  }, [searchParams, cartData]);

  useEffect(() => {
    setValue('paymentMethod', paymentMethod as OrderFormData['paymentMethod']);
    setValue('selectedCard', selectedCard);
    trigger();
  }, [paymentMethod, selectedCard, setValue, trigger]);

  useEffect(() => {
    if (hasPhysicalItem) {
      setSchema(createCartOrderFormSchema(hasPhysicalItem));
    }
  }, [hasPhysicalItem, fanmeCustomer, setValue, reset]);

  useEffect(() => {
    reset();
    setValue('address', fanmeCustomer?.prefecture);
  }, [reset, schema, fanmeCustomer, setValue]);

  const { checkCartItems } = useCheckCartItemsStatus({
    cartItems: cartItems || [],
    identityId,
    renderErrorContent: (errors) => <CartStatusError errors={errors} cartItems={cartItems || []} />,
    onComplete: () => {
      router.push(`/@${identityId}/cart`);
    },
  });

  const { processPayment, isProcessing } = useCartOrderProcess({
    payment: { paymentMethod, selectedCard, convenienceParam },
    cart: { cartId, cartItemIds, cartItems: cartItems || [] },
    amount: { tip, totalAmount },
    identityId,
    callbacks: { clearOtherPaymentParams, setConvenienceResult, checkCartItems },
    trigger,
  });

  if (!cartItems?.length) return <></>;

  return (
    <OrderPageTemplate
      tipLimitAmount={tipLimit || 0}
      cartItemsTotalPrice={cartItemsTotalPrice}
      fee={fee}
      tip={tip}
      deliveryFee={cartData?.data?.deliveryFee}
      yellCount={yellCount}
      boostRatio={boostRatio}
      hasShopCreatorActiveEvent={hasShopCreatorActiveEvent}
      identityId={identityId}
      isValid={isValid && !isProcessing}
      onProcessPayment={processPayment}
    >
      {hasPhysicalItem && (
        <section className="section-double-border mb-4 w-full px-4 pb-4">
          <SectionTitle title="配送先" className="mb-4" />
          <AddressSection />
        </section>
      )}
      <section className="w-full px-4 pb-4">
        <SectionTitle title="商品" className="mb-4" />
        <CartItemList showPurchaseInputsConfirm={true} items={cartItems || []} />
      </section>
    </OrderPageTemplate>
  );
};

export default CartOrderPage;
