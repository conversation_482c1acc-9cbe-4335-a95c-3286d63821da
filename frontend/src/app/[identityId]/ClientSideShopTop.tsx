'use client';
import { useEffect, useMemo, useRef, useState } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import clsx from 'clsx';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import SideButton from '@/components/atoms/button/side-button';
import NotificationWithNum from '@/components/atoms/notification/notification-with-num';
import ShareIcon from '@/components/atoms/shareIcon';
import CreatorInfo from '@/components/containers/creator-info';
import ReadMoreButton from '@/components/containers/ReadMoreButton';
import FixedBar from '@/components/layouts/FixedBar';
import ShopPublicImage from '@/components/ShopImage';
import { useGetShop } from '@/lib/client-api/shop-endpoint/shop-endpoint';
import { FeatureFlags } from '@/lib/feature';
import { ShopData } from '@/lib/server-api/shop-api.schemas';
import { useCurrentUser } from '@/store/useCurrentUser';
import { useFullModalStore } from '@/store/useFullModal';
import { useIsEdit } from '@/store/useIsEdit';
import { useShopItemsStore } from '@/store/useItems';
import { useSorting } from '@/store/useSorting';
import ItemsSection from './ItemsSection';
import ItemSkeleton from './TopPageSkelton';
import EditItemsSection from '@/app/[identityId]/EditItemsSection';
import { generateShopUrl } from '@/consts/url';
import { useGetCart } from '@/hooks/swr/useGetCart';
import { useIsFixedAtBottom } from '@/hooks/useIsFixedAtBottom';
import { shareShopPage } from '@/utils/share';
import { ITEM_TYPE, ItemTypeString, ItemTypeValue } from '@/types/item';
import { ShopItemType } from '@/types/shopItem';

type ClientSideShopTopProps = {
  shopData: ShopData;
  shopItems: ShopItemType[];
  identityId: string;
};

type MenuItem = {
  id: ItemTypeValue;
  itemType: ItemTypeString;
  src: string;
  alt: string;
};

const ClientSideShopTop = ({ shopData, shopItems, identityId }: ClientSideShopTopProps) => {
  const router = useRouter();
  const midDiv = useRef<HTMLDivElement>(null);
  const isFixed = useIsFixedAtBottom(midDiv);
  const { currentUser, isOwner, setIsOwner } = useCurrentUser();
  const { setIsEdit, isEdit } = useIsEdit();
  const { sorting } = useSorting();
  const { itemList, setItemList } = useShopItemsStore();
  const [isAvailableItems, setIsAvailableItems] = useState<boolean>();
  const { data: shopResponseBody } = useGetShop(identityId);
  const shopLimitation = shopResponseBody?.data?.shop.limitation;

  const menuItems = useMemo<MenuItem[]>(() => {
    const items: MenuItem[] = [
      {
        id: ITEM_TYPE.DIGITAL_BUNDLE.value,
        itemType: ITEM_TYPE.DIGITAL_BUNDLE.str,
        src: '/shop/images/bannerDigitalContents.webp',
        alt: 'Digital Contents',
      },
    ];

    if (FeatureFlags.gacha()) {
      items.push({
        id: ITEM_TYPE.DIGITAL_GACHA.value,
        itemType: ITEM_TYPE.DIGITAL_GACHA.str,
        src: '/shop/images/bannerDigitalGacha.webp',
        alt: 'Digital Gacha',
      });
    }

    if (FeatureFlags.cheki() && shopLimitation?.isChekiExhibitable) {
      items.push({
        id: ITEM_TYPE.CHEKI.value,
        itemType: ITEM_TYPE.CHEKI.str,
        src: '/shop/images/bannerCheki.webp',
        alt: 'Cheki',
      });
    }

    return items;
  }, [shopLimitation]);

  const {
    headerImageUri: coverImage,
    description,
    name: shopName,
    creatorName,
    creatorIconUri: creatorIcon,
  } = shopData.shop;

  const { onFullModalOpen, setFullModalProps, onFullModalClose } = useFullModalStore();

  const handleShare = async () => {
    sendGTMEvent({ event: 'fanme_item_list_share', item_id: identityId });
    const url = generateShopUrl(identityId);
    const shareTitle = `${shopName} - ${creatorName} | FANME（ファンミー）`;
    await shareShopPage({
      identityId,
      shopName,
      creatorName,
      url,
      shareTitle,
    });
  };

  useEffect(() => {
    if (itemList?.length === 0 || !itemList) setItemList(shopItems);
    else setItemList(itemList);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemList, shopItems]);

  const handleCreateItem = (itemType: ItemTypeValue = ITEM_TYPE.DIGITAL_BUNDLE.value) => {
    const tempItemId = 'new' + Date.now();
    let eventType = 'fanme_listing_digital_bundle_click';
    let url = `/@${identityId}/item/${tempItemId}/create`;

    switch (itemType) {
      case ITEM_TYPE.DIGITAL_GACHA.value:
        eventType = 'fanme_listing_digital_gacha_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=digitalGacha`;
        break;
      case ITEM_TYPE.CHEKI.value:
        eventType = 'fanme_listing_cheki_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=cheki`;
        break;
    }

    sendGTMEvent({ event: eventType, shop_id: identityId });
    router.push(url);
  };

  const { data: cartData } = useGetCart(identityId, identityId);
  const onClickShopEditButton = () => {
    router.push(`/@${identityId}/edit?t=${Date.now()}`);
  };

  const searchParams = useSearchParams();
  useEffect(() => {
    if (searchParams.has('editMode')) {
      setIsEdit(true);
    } else {
      setIsEdit(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  useEffect(() => {
    if (currentUser) {
      setIsOwner(identityId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser]);

  useEffect(() => {
    if (itemList && itemList.length > 0) {
      itemList.forEach((item) => {
        if (isAvailableItems) return;
        let isForSale = false;
        if (item.forSale?.startAt === null && item.forSale?.endAt == null) {
          isForSale = true;
        } else if (!!item.forSale?.endAt && new Date(item.forSale?.endAt).getTime() > new Date().getTime()) {
          isForSale = true;
        }
        if (item.available && isForSale) {
          setIsAvailableItems(true);
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemList]);

  const selectItem = (itemType: ItemTypeValue) => {
    handleCreateItem(itemType);
    onFullModalClose();
  };

  const MenuItems = () => {
    return (
      <div className="mx-1 mt-3 space-y-3">
        {menuItems.map((item) => (
          <button
            onClick={() => selectItem(item.id)}
            key={item.id}
            className="flex flex-col items-center justify-center rounded-md bg-white px-1.5 py-2"
          >
            <Image
              src={item.src}
              width={480}
              height={160}
              priority
              className="w-full object-fill"
              alt={`${item.alt} banner`}
            />
          </button>
        ))}
      </div>
    );
  };

  // 出品メニューのモダルを出す
  const openItemMenuModal = () => {
    onFullModalOpen();
    setFullModalProps({
      title: '出品メニュー',
      children: <MenuItems />,
      onClose: onFullModalClose,
    });
  };

  return (
    <>
      <div
        ref={midDiv}
        className={clsx('relative flex flex-1 flex-col', {
          ['pb-30']: !isFixed && isEdit,
          ['pb-2']: !isEdit,
        })}
      >
        <div className="fixed bottom-24 right-[calc((100vw-480px)/2)] z-10 max-PC:right-0">
          {(!isOwner || (isOwner && !isEdit)) && (
            <>
              {!!cartData?.data.totalQuantity && (
                <NotificationWithNum num={cartData?.data.totalQuantity} className="absolute right-0 top-0 z-10" />
              )}
              <SideButton
                sideButtonType="dark"
                buttonDirection="left"
                buttonSize="lg"
                onClick={() => router.push(`/@${identityId}/cart`)}
              >
                <ShopPublicImage src="/images/icons/Cart.svg" width={28} height={28} alt="cart" />
              </SideButton>
            </>
          )}
        </div>
        {!!coverImage && (
          <div className="w-full">
            <Image src={coverImage} width={480} height={160} priority className="w-full object-fill" alt="shop cover" />
          </div>
        )}
        <div className="rounded-b-2xl bg-white p-3">
          <div className="mb-4 flex items-center justify-between px-1">
            <CreatorInfo creatorName={creatorName} identityId={identityId} creatorIcon={creatorIcon} />
            <button className="p-2" onClick={handleShare}>
              <ShareIcon />
            </button>
          </div>
          <div
            className={clsx('flex items-center justify-between', {
              ['mb-3']: !!description,
            })}
          >
            <h1 className="text-bold-15">{shopName}</h1>
            {isOwner && isEdit && !sorting && (
              <OutlinedButton
                buttonColor="black"
                buttonShape="pill"
                buttonType="edit"
                onClick={onClickShopEditButton}
              />
            )}
          </div>
          {description && <ReadMoreButton description={description} />}
        </div>
        {(!isOwner || (isOwner && !isEdit)) &&
          (!itemList ? (
            <ItemSkeleton />
          ) : itemList.length === 0 || !isAvailableItems ? (
            <div className="flex flex-1 items-center justify-center text-center">
              <div className="flex w-full flex-col items-center gap-4">
                <ShopPublicImage src="/images/icons/shopIcon.svg" width={53.33} height={64} alt="empty" />
                <p className="text-bold-18 text-gray-500">商品がまだ登録されていません</p>
                <p className="text-medium-14 text-gray-500">しばらく経ってからまたお越しください</p>
              </div>
            </div>
          ) : itemList?.length > 0 ? (
            <ItemsSection identityId={identityId} items={itemList} />
          ) : (
            <></>
          ))}
        <div className="flex-1">
          {isOwner &&
            isEdit &&
            (!!itemList && itemList.length > 0 ? (
              <EditItemsSection identityId={identityId} />
            ) : (
              <div className="mb-10 mt-8 flex flex-col items-center justify-center">
                <h2 className="text-bold-20 text-orange-200">商品を出品してみましょう</h2>
                <div className="relative mt-8 w-full px-4">
                  <div className="flex flex-col items-center rounded-base bg-white p-2 shadow">
                    <ShopPublicImage src="/images/Step1.webp" width={344} height={64} alt="step 1" className="mb-4" />
                    <ShopPublicImage src="/images/Step2.webp" width={344} height={64} alt="step 2" />
                  </div>
                  <div className="absolute -bottom-4 left-1/2 -translate-x-1/2 drop-shadow-xxs">
                    <div className="size-0 border-x-[16px] border-t-[16px] border-solid border-x-transparent border-t-white"></div>
                  </div>
                </div>
              </div>
            ))}
        </div>
        {isOwner && isEdit && !sorting && (
          <FixedBar isFixed={isFixed}>
            <Button
              buttonType="dark"
              onClick={FeatureFlags.cheki() || FeatureFlags.gacha() ? openItemMenuModal : () => handleCreateItem()}
            >
              出品する
            </Button>
          </FixedBar>
        )}
      </div>
    </>
  );
};

export default ClientSideShopTop;
