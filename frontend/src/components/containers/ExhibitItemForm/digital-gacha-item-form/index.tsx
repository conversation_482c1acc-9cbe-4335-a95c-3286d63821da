'use client';

import GachaThumbnailSection from '@/components/containers/GachaThumbnailSection';
import GachaUploadSection from '@/components/containers/GachaUploadSection';
import ItemMainInfo from '@/components/containers/ItemMainInfo';
import ItemOptionsSection from '@/components/containers/ItemOptionsSection';
import SampleSection from '@/components/containers/SampleSection';
import PublicSetSection from '@/components/views/PublicSetSection';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';

const DigitalGachaItemForm = ({ shopLimitation, isEdit }: { shopLimitation: ShopLimitation; isEdit?: boolean }) => {
  return (
    <>
      <GachaUploadSection numbering="01" shopLimitation={shopLimitation} isEdit={isEdit} />
      <ItemMainInfo numbering="02" />
      <GachaThumbnailSection numbering="03" />
      {/* 特典 ０４ secondでする*/}
      <SampleSection numbering="04" shopLimitation={shopLimitation} />
      <ItemOptionsSection numbering="05" isEdit={isEdit} />
      <PublicSetSection numbering="06" />
    </>
  );
};

export default DigitalGachaItemForm;
