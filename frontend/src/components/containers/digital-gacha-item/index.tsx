'use client';

import clsx from 'clsx';
import DiscountBadge from '@/components/atoms/DiscountBadge';
import ItemIcon from '@/components/atoms/itemIcon';
import type { ItemResponseBody } from '@/lib/client-api/client-shop-api.schemas';

type BaseItem = NonNullable<ItemResponseBody['data']>['item'];
type Item = Pick<BaseItem, 'name' | 'thumbnailUri' | 'price' | 'currentPrice' | 'itemOption'>;

interface IDigitalGachaItemProps {
  item: Item;
  quantity: number;
  className?: string;
}

const DigitalGachaItem = ({ item, className, quantity }: IDigitalGachaItemProps) => {
  const { name: title, thumbnailUri: imageUrl, price, currentPrice, itemOption } = item;

  const discountRate = (itemOption.onSale?.discountRate ?? 0) * 100;
  const shouldShowOnSaleInfo = !!currentPrice && !!discountRate;

  return (
    <div className={clsx('relative flex h-32.5 w-full bg-white', className)}>
      <ItemIcon thumbnail={imageUrl} title={title} size={130} radius="none" thumbnailRatio={1} />
      <div className="flex w-full flex-col items-start justify-between px-1 py-1 sm:px-4">
        <div className="flex max-h-9 min-h-9 w-full items-center overflow-hidden">
          <h3 className="line-clamp-2 overflow-hidden text-ellipsis break-all text-medium-13">{title}</h3>
        </div>
        <div className="flex w-full items-center justify-between">
          <div className="flex flex-col">
            {shouldShowOnSaleInfo ? (
              <>
                <div className="flex items-center gap-1">
                  <div className="text-bold-16 sm:text-bold-20">¥{currentPrice.toLocaleString()}</div>
                  <span className="text-regular-9">(税込)</span>
                  {quantity && quantity > 1 && <span className="ml-1 text-medium-16">×{quantity}</span>}
                  <DiscountBadge percentage={discountRate} className="mr-2" />
                </div>
                <div className="text-regular-12 text-gray-400">
                  通常価格 <span className="line-through">¥{price?.toLocaleString()}</span>
                </div>
              </>
            ) : (
              <div className="flex items-center gap-1">
                <div className="text-bold-20">¥{price?.toLocaleString()}</div>
                <span className="text-regular-8">(税込)</span>
                {quantity > 1 && <span className="ml-2 text-medium-16">×{quantity}</span>}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DigitalGachaItem;
