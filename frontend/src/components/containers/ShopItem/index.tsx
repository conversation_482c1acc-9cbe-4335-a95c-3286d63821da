import React, { useMemo } from 'react';
import clsx from 'clsx';
import Link from 'next/link';
import GoodsTypesBadge from '@/components/atoms/badges/goods-types-badge';
import StateBadge from '@/components/atoms/badges/state-badge';
import RippleButton from '@/components/atoms/button/ripple-button';
import DiscountBadge from '@/components/atoms/DiscountBadge';
import ItemIcon from '@/components/atoms/itemIcon';
import CircleItemIcon from '@/components/atoms/itemIcon/circle-item-icon';
import ShopPublicImage from '@/components/ShopImage';
import { GachaCompleteProgressBarWithIcon } from '../GachaCompleteProgressBarWithIcon';
import ForSaleBadge from './ForSaleBadge';
import OnSaleBadge from './OnSaleBadge';
import { bytesToMB, formatDuration } from '@/utils/base';
import { convertToSetTypeMap, getIsNotEndForSale, getIsNowOnSale } from '@/utils/item';
import { ITEM_TYPE } from '@/types/item';
import { ForSale, OnSale } from '@/types/shopItem';

type ShopItemProps = {
  itemId: number;
  identityId: string;
  thumbnail: string;
  isNew?: boolean;
  title: string;
  price: number;
  currentPrice: number;
  minPrice: number;
  fileType?: string;
  itemType?: number;
  setType?: string;
  isSet?: boolean;
  isAdded?: boolean;
  isPurchased?: boolean;
  isCheckout?: boolean;
  hasPassword?: boolean;
  onSale?: OnSale | undefined;
  forSale?: ForSale;
  isSingleSales?: boolean;
  hasBenefit?: boolean;
  remainingAmount?: number;
  extension?: string;
  duration?: number;
  isExpired?: boolean;
  size?: number;
  isStarted?: boolean;
  onAddToCart?: (e: React.MouseEvent, type: 'single' | 'set', itemId: string) => void;
  isPreview?: boolean;
  onShopItemEvent?: (itemId: number, e: React.MouseEvent) => void;
  collectedUniqueItemsCount?: number;
  totalItemFileCount?: number;
};

const ShopItem = ({
  itemId,
  identityId,
  thumbnail,
  isNew,
  title,
  price,
  currentPrice,
  minPrice,
  fileType,
  itemType,
  setType,
  isSet = false,
  isAdded,
  isPurchased,
  isCheckout,
  hasPassword = false,
  onSale,
  forSale,
  isSingleSales,
  remainingAmount,
  duration,
  isExpired,
  size,
  isStarted,
  onAddToCart,
  hasBenefit = false,
  isPreview,
  onShopItemEvent,
  collectedUniqueItemsCount,
  totalItemFileCount,
}: ShopItemProps) => {
  const setTypeMap = setType ? convertToSetTypeMap(setType) : null;
  const isNowOnSale = getIsNowOnSale(onSale);
  const isNotEndForSale = getIsNotEndForSale(isNowOnSale, forSale);

  const singleItemIcon = useMemo(() => {
    if (isSet) return <ItemIcon thumbnail={thumbnail} thumbnailRatio={1} title={title} size={136} radius="lxl-l" />;

    if (fileType === 'image') {
      return (
        <div className="relative flex size-full flex-col items-center justify-center rounded-l-xl bg-secondary">
          <ShopPublicImage
            src="/images/icons/photoGroupIcon.svg"
            alt="image"
            width={38}
            height={55}
            className="brightness-1000"
          />
        </div>
      );
    } else if (fileType === 'video') {
      return (
        <div className="relative flex size-full flex-col items-center justify-center rounded-l-xl bg-secondary">
          <ShopPublicImage
            src="/images/icons/movieGroupIcon.svg"
            alt="video"
            width={38}
            height={55}
            className="brightness-1000"
          />
        </div>
      );
    } else if (fileType === 'audio') {
      return (
        <div className="relative flex size-full flex-col items-center justify-center rounded-l-xl bg-secondary">
          <ShopPublicImage
            src="/images/icons/audioGroupIcon.svg"
            alt="audio"
            width={38}
            height={55}
            className="brightness-1000"
          />
        </div>
      );
    }
    return null;
  }, [isSet, fileType, thumbnail, title]);

  const handleCardClick = (e: React.MouseEvent) => {
    if (onShopItemEvent) {
      onShopItemEvent(itemId, e);
    }
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    if (!itemId) return;
    e.stopPropagation();
    onAddToCart?.(e, 'single', itemId.toString());
  };

  return (
    <div className={clsx(`relative flex w-full items-center`, isSet ? 'h-32' : 'h-30')}>
      <div
        onClick={onShopItemEvent ? handleCardClick : undefined}
        className={clsx(
          'flex size-full select-none items-center gap-x-3',
          { 'border-2 border-navy-50 shadow-light': isSet },
          'bg-white',
          'rounded-xl transition-all duration-75',
        )}
      >
        {remainingAmount && (
          <div className="absolute left-0 top-0 size-25">
            <div className="absolute -left-3 -top-3 z-30">
              <ShopPublicImage src="/images/LimitedSale_full.png" alt="Limited Sale badge" width={100} height={100} />
            </div>
            <div className={clsx('absolute inset-0.5 z-40 mt-1 flex -rotate-45 items-start justify-center')}>
              <span className="text-bold-16 text-white">{remainingAmount.toLocaleString()}</span>
            </div>
          </div>
        )}

        {isStarted && !isPreview && (
          <div className="absolute inset-x-0 top-0 z-40 mx-auto flex size-full items-center justify-center rounded-xl bg-transparent-white">
            <div className="h-8 w-32 bg-blue-200 text-center text-medium-13 leading-8 text-white">まもなく販売</div>
          </div>
        )}

        <div className={clsx(`relative h-full w-31 shrink-0`, isSet && 'py-[0.1rem] pl-[0.1rem]')}>
          {isSet ? (
            itemType === ITEM_TYPE.DIGITAL_GACHA.value ? (
              <div className="h-full overflow-hidden rounded-l-xl">
                <CircleItemIcon
                  thumbnail={thumbnail}
                  title={title}
                  circleBgUrl="url(/shop/images/gacha/shopItemBg.webp)"
                  circleBorder={{ color: 'light', width: 6, style: 'solid' }}
                  width={124}
                />
              </div>
            ) : (
              <ItemIcon thumbnail={thumbnail} thumbnailRatio={1} title={title} size={136} radius="lxl-l" />
            )
          ) : (
            singleItemIcon
          )}
          <div className="absolute inset-0 mb-2 flex items-end justify-center">
            <ForSaleBadge forSale={forSale} isShowOnSaleBadge={isNowOnSale} isShowForSaleBadge={isNotEndForSale} />
            <OnSaleBadge onSale={onSale} isShowOnSaleBadge={isNowOnSale} isShowForSaleBadge={isNotEndForSale} />
          </div>
          <div className="absolute right-2 top-2">
            {!isPurchased && isAdded && (
              <StateBadge type="square-lined" color="lined-orange" size="sm">
                追加済み
              </StateBadge>
            )}
            {isCheckout && (
              <StateBadge type="square-lined" color="lined-blue" size="lg">
                お支払い待ち
              </StateBadge>
            )}
            {isPurchased && itemType !== ITEM_TYPE.DIGITAL_GACHA.value && (
              <StateBadge type="square-lined" color="lined-green" size="sm">
                購入済み
              </StateBadge>
            )}
          </div>
          {hasPassword && (
            <div className="absolute inset-0 flex items-center justify-center">
              <ShopPublicImage src="/images/Pass.webp" alt="time" width={46} height={46} />
            </div>
          )}
        </div>

        <div className="flex h-32 w-full flex-col justify-between pb-2 pr-2 pt-3">
          <div
            className={clsx(
              'flex w-full items-center overflow-hidden',
              isNowOnSale ? 'max-h-9 min-h-9' : 'max-h-14 min-h-14',
            )}
          >
            <h3
              className={clsx(
                isNowOnSale ? 'line-clamp-2' : 'line-clamp-3',
                'w-full overflow-hidden text-ellipsis break-all text-medium-13',
              )}
            >
              {isNew && (
                <span className={clsx('mr-2 text-bold-15 text-pink-100', title.length <= 32 ? 'block' : 'inline')}>
                  NEW!
                </span>
              )}
              {title}
            </h3>
          </div>
          {!!size && (
            <div className="text-regular-12">
              <span>{bytesToMB(size).toFixed(2)}MB</span> {!!duration && <span>({formatDuration(duration)})</span>}
            </div>
          )}

          <div className="flex w-full items-center justify-between">
            <div className="flex flex-col">
              {isNowOnSale && onSale?.discountRate && onSale.discountRate > 0 && currentPrice ? (
                <>
                  <div className="flex items-center">
                    <div className="mr-1 text-bold-20">¥{currentPrice.toLocaleString()}</div>
                    <span className="mr-1 text-regular-10">(税込)</span>
                    <DiscountBadge percentage={onSale.discountRate} className="mr-2" />
                  </div>
                  <div className="text-regular-12 text-gray-400">
                    通常価格 <span className="line-through">¥{price.toLocaleString()}</span>
                  </div>
                </>
              ) : (
                <div className="flex items-center">
                  <div className="mr-1 text-bold-20">
                    ¥{minPrice.toLocaleString()}
                    {isSingleSales && '〜'}
                  </div>
                  <span className="text-regular-10">(税込)</span>
                </div>
              )}
            </div>
            <div>
              {!isSet && !hasPassword && !isExpired && !isAdded && !isPurchased && !isCheckout && (
                <RippleButton
                  onClick={handleAddToCart}
                  className="ml-2 inline-block h-7 w-18 rounded-full border border-yellow-100 bg-yellow-200 text-center text-medium-11 leading-6"
                >
                  追加
                </RippleButton>
              )}
              {!isPurchased && isAdded && (
                <RippleButton
                  disabled
                  className="ml-2 inline-block h-7 w-18 rounded-full border text-center text-medium-11 leading-6"
                >
                  追加済み
                </RippleButton>
              )}
              {!isSet && isCheckout && (
                <RippleButton
                  disabled
                  className="ml-2 inline-block h-7 w-20 rounded-full border text-center text-medium-11 leading-6"
                >
                  お支払い待ち
                </RippleButton>
              )}
              {!isSet && isPurchased && (
                <Link href={`/@${identityId}/${isPurchased ? 'viewer' : 'item'}/${itemId}`}>
                  <RippleButton className="ml-2 inline-block h-7 w-18 rounded-full border text-center text-medium-11 leading-6">
                    購入済み
                  </RippleButton>
                </Link>
              )}
            </div>
          </div>
          {itemType === ITEM_TYPE.DIGITAL_GACHA.value ? (
            <GachaCompleteProgressBarWithIcon
              collectedUniqueItemsCount={collectedUniqueItemsCount || 0}
              total={totalItemFileCount!}
            />
          ) : (
            setTypeMap && (
              <div className="mt-1 flex gap-2">
                <GoodsTypesBadge
                  activeImage={setTypeMap.image}
                  activeVideo={setTypeMap.video}
                  activeAudio={setTypeMap.audio}
                  activeTokuten={hasBenefit}
                />
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default ShopItem;
