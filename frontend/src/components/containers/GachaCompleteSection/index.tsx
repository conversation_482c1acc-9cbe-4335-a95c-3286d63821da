'use client';
import clsx from 'clsx';
import Instruction from '@/components/atoms/typography/instruction';
import GachaCompleteBadge from '@/components/containers/GachaCompleteBadge';
import ShopPublicImage from '@/components/ShopImage';
import { useGetCompleteBadge } from '@/lib/client-api/digital-gacha-endpoint/digital-gacha-endpoint';
import { roboto } from '@/app/fonts';

type GachaCompleteSectionProps = {
  isPreview: boolean;
  totalGachaCount: number;
  collectedUniqueItemsCount: number;
  isCompleted: boolean;
  thumbnail: string;
  itemId: number;
};

const GachaCompleteSection = ({
  props: { isPreview, totalGachaCount, collectedUniqueItemsCount, isCompleted, thumbnail, itemId },
}: {
  props: GachaCompleteSectionProps;
}) => {
  const { data: completeBadgeData } = useGetCompleteBadge(itemId, {
    swr: {
      enabled: isCompleted && !isPreview,
    },
  });

  return (
    <div className="flex flex-col items-center justify-center rounded-t-2xl bg-white pb-4 pt-6 drop-shadow-s">
      <div className="mb-2 flex justify-center">
        <ShopPublicImage
          src="/images/gacha/icons/gacha_complete_description.svg"
          alt="Gacha Complete Description"
          width={296}
          height={72}
        />
      </div>
      <div className="w-86">
        {isCompleted && completeBadgeData?.data?.badge.rank && (
          <GachaCompleteBadge creatorAvatar={thumbnail} completeRank={completeBadgeData.data.badge.rank} />
        )}
        <div className={clsx('flex items-center', isCompleted ? 'justify-center' : 'justify-end')}>
          {/* Left section */}
          <div className={clsx(isCompleted ? 'w-55' : 'w-24')}>
            {isCompleted && (
              <div className={clsx('flex h-8 items-center justify-start gap-5 rounded-lg bg-gray-700 px-2')}>
                <ShopPublicImage src="/images/gacha/icons/gacha_white.svg" alt="gacha icon" width={20} height={20} />
                <div className="flex items-center gap-1 text-white">
                  <span className={clsx('gradient-complete-text text-bold-20', roboto.className)}>COMPLETED!!</span>/{' '}
                  {totalGachaCount}
                </div>
              </div>
            )}
          </div>

          {/* Ranking button */}
          {/* first releaseではない　一旦コメントアウト */}
          {/* <div className="flex justify-center">
            <Link
              href={`/ranking`}
              className="flex h-8 items-center justify-center gap-1 rounded-2xl border border-secondary bg-white px-3 text-bold-12"
            >
              <ShopPublicImage src="/images/icons/Ranking.svg" alt="Ranking" width={16} height={16} />
              ランキング
            </Link>
          </div> */}

          {/* Gacha count */}
          {!isCompleted && (
            <div className="flex items-center gap-2">
              <ShopPublicImage src="/images/gacha/icons/gacha_bk.svg" alt="Gacha" width={20} height={20} />
              <div
                className={clsx(collectedUniqueItemsCount > 0 ? 'text-secondary' : 'text-gray-400', roboto.className)}
              >
                <span className="text-bold-20">{isPreview ? totalGachaCount : collectedUniqueItemsCount}</span>
                <span className="text-regular-16">/{totalGachaCount}</span>
              </div>
            </div>
          )}
        </div>
        {isPreview && <Instruction className="mt-3">*図鑑はコンプリートしたときのイメージです</Instruction>}
      </div>
    </div>
  );
};

export default GachaCompleteSection;
