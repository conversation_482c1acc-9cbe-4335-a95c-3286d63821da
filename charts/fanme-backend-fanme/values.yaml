# Default values for fanme-backend-fanme.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
hpa: false
maxReplicas: 20
targetCPUUtilization: 80
pdb: true
minAvailable: 1
image:
  fanme-backend-fanme:
    repository: 638984414044.dkr.ecr.ap-northeast-1.amazonaws.com/fanme-backend
    tag: a77f29d667836fc49e7f79e2c72ea0ed1326ec61
    pullPolicy: Always
nameOverride: ""
fullnameOverride: ""
ingress:
  enabled: false
  className: ""
  annotations: {}
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  path: /
  hosts:
    - chart-example.local
  tls: []
resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 20m
    memory: 32Mi
nodeSelector: {}
tolerations: []
affinity: {}
fanme-backend-fanme:
  database:
    username: ""
    password: ""
    host: ""
    port: ""
    name: ""
env: ""
